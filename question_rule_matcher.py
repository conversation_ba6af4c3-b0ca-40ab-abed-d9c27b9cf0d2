"""
用户问题规则匹配模块
根据规则测试文件，从用户问题中识别并提取相关元素
"""

import pandas as pd
import re
import logging
from typing import Dict, List, Tuple, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QuestionRuleMatcher:
    def __init__(self, rule_file="规则测试.xlsx", sheet_name=None):
        """
        初始化问题规则匹配器

        参数:
            rule_file (str): 规则测试文件路径
            sheet_name (str): 工作表名称，如果为None则使用第一个工作表
        """
        self.rule_file = rule_file
        self.sheet_name = sheet_name
        self.rules_df = None
        self.rules_dict = {}
        self.load_rules()

    def load_rules(self):
        """加载规则文件"""
        try:
            logger.info(f"正在加载规则文件: {self.rule_file}")

            # 读取Excel文件
            if self.sheet_name:
                self.rules_df = pd.read_excel(self.rule_file, sheet_name=self.sheet_name)
            else:
                self.rules_df = pd.read_excel(self.rule_file)

            logger.info(f"成功加载规则文件，共 {len(self.rules_df)} 行数据")
            logger.info(f"列名: {list(self.rules_df.columns)}")

            # 构建规则字典
            self.build_rules_dict()

        except Exception as e:
            logger.error(f"加载规则文件失败: {str(e)}")
            raise

    def build_rules_dict(self):
        """构建规则字典，用于快速匹配"""
        if self.rules_df is None or len(self.rules_df.columns) < 2:
            logger.error("规则文件格式不正确，需要至少2列")
            return

        # 获取第一列和第二列的列名
        first_col = self.rules_df.columns[0]
        second_col = self.rules_df.columns[1]

        logger.info(f"使用列: 第一列='{first_col}', 第二列='{second_col}'")

        # 构建规则字典
        self.rules_dict = {}

        for index, row in self.rules_df.iterrows():
            category = row[first_col]  # 第一列：类别/键名
            value = row[second_col]    # 第二列：具体值/元素

            # 跳过空值
            if pd.isna(category) or pd.isna(value):
                continue

            category = str(category).strip()
            value = str(value).strip()

            if not category or not value:
                continue

            # 将值添加到对应类别中
            if category not in self.rules_dict:
                self.rules_dict[category] = []

            self.rules_dict[category].append(value)

        logger.info(f"构建规则字典完成，共 {len(self.rules_dict)} 个类别")
        for category, values in self.rules_dict.items():
            logger.info(f"  {category}: {len(values)} 个元素")

    def preprocess_text(self, text: str) -> str:
        """预处理文本，去除特殊字符和多余空格"""
        if not text:
            return ""

        # 去除多余空格
        text = re.sub(r'\s+', '', text)

        return text

    def find_longest_match(self, question: str, values: List[str]) -> Optional[str]:
        """
        在问题中查找最长匹配的值

        参数:
            question (str): 用户问题
            values (List[str]): 候选值列表

        返回:
            str: 最长匹配的值，如果没有匹配则返回None
        """
        # 预处理问题文本
        processed_question = self.preprocess_text(question)

        # 按长度降序排序，优先匹配更长的字符串
        sorted_values = sorted(values, key=len, reverse=True)

        for value in sorted_values:
            processed_value = self.preprocess_text(value)

            # 检查是否包含
            if processed_value and processed_value in processed_question:
                logger.debug(f"找到匹配: '{value}' 在问题中")
                return value

        return None

    def match_question(self, question: str) -> Dict[str, str]:
        """
        匹配用户问题，仅返回最大（最长）的匹配结果

        参数:
            question (str): 用户问题

        返回:
            Dict[str, str]: 匹配到的键值对字典（仅包含最长匹配）
        """
        if not question or not self.rules_dict:
            return {}

        logger.info(f"开始匹配问题: {question}")

        all_matches = []

        # 遍历所有规则类别，收集所有匹配项
        for category, values in self.rules_dict.items():
            # 查找最长匹配
            matched_value = self.find_longest_match(question, values)

            if matched_value:
                all_matches.append({
                    'category': category,
                    'value': matched_value,
                    'length': len(self.preprocess_text(matched_value))
                })
                logger.debug(f"找到匹配: {category} = {matched_value} (长度: {len(self.preprocess_text(matched_value))})")

        # 如果没有匹配项，返回空字典
        if not all_matches:
            logger.info("未找到任何匹配项")
            return {}

        # 找到最长的匹配项
        longest_match = max(all_matches, key=lambda x: x['length'])

        result = {longest_match['category']: longest_match['value']}

        logger.info(f"匹配完成，返回最长匹配: {longest_match['category']} = {longest_match['value']} (长度: {longest_match['length']})")
        return result

    def match_question_all(self, question: str) -> Dict[str, str]:
        """
        匹配用户问题，返回所有找到的键值对（保留原来的功能）

        参数:
            question (str): 用户问题

        返回:
            Dict[str, str]: 匹配到的所有键值对字典
        """
        if not question or not self.rules_dict:
            return {}

        logger.info(f"开始匹配问题（返回所有匹配）: {question}")

        result = {}

        # 遍历所有规则类别
        for category, values in self.rules_dict.items():
            # 查找最长匹配
            matched_value = self.find_longest_match(question, values)

            if matched_value:
                result[category] = matched_value
                logger.info(f"匹配成功: {category} = {matched_value}")

        logger.info(f"匹配完成，共找到 {len(result)} 个匹配项")
        return result

    def match_question_detailed(self, question: str) -> Dict:
        """
        详细匹配用户问题，返回详细信息

        参数:
            question (str): 用户问题

        返回:
            Dict: 包含匹配结果和详细信息的字典
        """
        matches = self.match_question(question)

        # 统计信息
        total_categories = len(self.rules_dict)
        matched_categories = len(matches)

        result = {
            'question': question,
            'matches': matches,
            'match_count': matched_categories,
            'total_categories': total_categories,
            'match_rate': matched_categories / total_categories if total_categories > 0 else 0,
            'unmatched_categories': [cat for cat in self.rules_dict.keys() if cat not in matches]
        }

        return result

    def get_category_values(self, category: str) -> List[str]:
        """
        获取指定类别的所有值

        参数:
            category (str): 类别名称

        返回:
            List[str]: 该类别的所有值
        """
        return self.rules_dict.get(category, [])

    def get_all_categories(self) -> List[str]:
        """
        获取所有类别名称

        返回:
            List[str]: 所有类别名称列表
        """
        return list(self.rules_dict.keys())

    def search_value_in_category(self, value: str, category: str = None) -> List[Tuple[str, str]]:
        """
        在指定类别或所有类别中搜索包含指定值的项

        参数:
            value (str): 要搜索的值
            category (str): 指定类别，如果为None则搜索所有类别

        返回:
            List[Tuple[str, str]]: 匹配的(类别, 值)元组列表
        """
        results = []

        search_dict = {category: self.rules_dict[category]} if category and category in self.rules_dict else self.rules_dict

        for cat, values in search_dict.items():
            for val in values:
                if value.lower() in val.lower():
                    results.append((cat, val))

        return results

    def export_rules_summary(self) -> Dict:
        """
        导出规则摘要信息

        返回:
            Dict: 规则摘要信息
        """
        summary = {
            'total_categories': len(self.rules_dict),
            'categories': {}
        }

        for category, values in self.rules_dict.items():
            summary['categories'][category] = {
                'count': len(values),
                'values': values[:5],  # 只显示前5个值作为示例
                'has_more': len(values) > 5
            }

        return summary

def match_question_simple(question: str, rule_file: str = "规则测试.xlsx") -> Dict[str, str]:
    """
    简化的问题匹配函数，直接返回匹配结果

    参数:
        question (str): 用户问题
        rule_file (str): 规则文件路径

    返回:
        Dict[str, str]: 匹配到的键值对字典
    """
    try:
        matcher = QuestionRuleMatcher(rule_file)
        return matcher.match_question(question)
    except Exception as e:
        logger.error(f"匹配过程中出现错误: {str(e)}")
        return {}

def main():
    """主函数，用于测试"""
    # 测试用例
    test_questions = [
        "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？",
        "昨天天天成长3号在小招的赎回金额？",
        "今天固定收益类在代销的净申赎金额是多少?",
        "2025年3月20日新启航三个月定开1号产品净申赎金额是多少？",
        "近三天固定收益类的持仓规模是多少?"
    ]

    print("🚀 开始测试问题规则匹配模块")
    print("=" * 80)

    try:
        # 创建匹配器
        matcher = QuestionRuleMatcher()

        # 显示规则摘要
        summary = matcher.export_rules_summary()
        print(f"📊 规则摘要:")
        print(f"总类别数: {summary['total_categories']}")
        for category, info in summary['categories'].items():
            print(f"  {category}: {info['count']} 个元素")
            if info['has_more']:
                print(f"    示例: {', '.join(info['values'])}...")
            else:
                print(f"    全部: {', '.join(info['values'])}")

        print("\n" + "=" * 80)

        # 测试每个问题
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔍 测试问题 {i}: {question}")

            # 简单匹配
            result = matcher.match_question(question)
            print(f"匹配结果: {result}")

            # 详细匹配
            detailed_result = matcher.match_question_detailed(question)
            print(f"匹配率: {detailed_result['match_rate']:.1%} ({detailed_result['match_count']}/{detailed_result['total_categories']})")

            if detailed_result['unmatched_categories']:
                print(f"未匹配类别: {detailed_result['unmatched_categories']}")

        print("\n" + "=" * 80)
        print("🎉 测试完成！")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
