{"processing_info": {"total_questions": 10, "successful": 10, "failed": 0, "processing_time": "2025-05-28T15:13:53.883489"}, "results": [{"index": 0, "question": "3. 提取本月第一天权益类产品的预约申购金额和预约赎回金额？", "extracted_keywords": {"数据日期": ["20250501-20250531"], "产品分类": ["权益"], "指标名": ["预约申购金额", "预约赎回金额"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250501-20250531\"],\n  \"产品分类\": [\"权益\"],\n  \"指标名\": [\"预约申购金额\", \"预约赎回金额\"]\n}\n```", "processing_time": 1.7602107524871826, "success": true, "error": null}, {"index": 1, "question": "9. 提取启航系列的产品，在近三天内各渠道的持仓客户数分布。", "extracted_keywords": {"数据日期": ["20250525-20250527"], "产品系列": ["启航"], "指标名": ["持仓客户数"], "渠道一级分类": ["代销", "零售", "对公"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250525-20250527\"],\n  \"产品系列\": [\"启航\"],\n  \"指标名\": [\"持仓客户数\"],\n  \"渠道一级分类\": [\"代销\", \"零售\", \"对公\"]\n}\n```", "processing_time": 2.150043249130249, "success": true, "error": null}, {"index": 2, "question": "5. 获取过去 20 天中， 中小行渠道且产品大类为 ' 固收 + ' 的产品的预约赎回金额汇总", "extracted_keywords": {"数据日期": ["20250508-20250527"], "渠道二级分类": ["代销 - 中小行"], "产品分类": ["固收+"], "指标名": ["预约赎回金额"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250508-20250527\"],\n  \"渠道二级分类\": [\"代销 - 中小行\"],\n  \"产品分类\": [\"固收+\"],\n  \"指标名\": [\"预约赎回金额\"]\n}\n```", "processing_time": 2.081796646118164, "success": true, "error": null}, {"index": 3, "question": "4. 哪种渠道一级分类的持仓规模最大，是否随时间变化而改变？", "extracted_keywords": {"渠道一级分类": ["代销", "零售", "对公"], "指标名": ["持仓规模"]}, "model_output": "```json\n{\n  \"渠道一级分类\": [\"代销\", \"零售\", \"对公\"],\n  \"指标名\": [\"持仓规模\"]\n}\n```", "processing_time": 2.9286131858825684, "success": true, "error": null}, {"index": 4, "question": "10. 持仓份额和持仓规模之间的比例关系在不同产品大类中是否一致？", "extracted_keywords": {"指标名": ["持仓份额", "持仓规模"], "产品分类": ["固定收益类", "固收 +", "混合", "权益", "现金类", "老理财"]}, "model_output": "```json\n{\n  \"指标名\": [\"持仓份额\", \"持仓规模\"],\n  \"产品分类\": [\"固定收益类\", \"固收 +\", \"混合\", \"权益\", \"现金类\", \"老理财\"]\n}\n```", "processing_time": 1.9978957176208496, "success": true, "error": null}, {"index": 5, "question": "19. 综合考虑渠道、产品大类和业务日期，哪些因素对净申赎金额（N_NET_SG_AMT）的影响最为显著？", "extracted_keywords": {"字段名1": ["渠道名称", "产品分类", "数据日期"], "字段名2": ["净申赎金额"]}, "model_output": "```json\n{\n  \"字段名1\": [\"渠道名称\", \"产品分类\", \"数据日期\"],\n  \"字段名2\": [\"净申赎金额\"]\n}\n```", "processing_time": 1.506784200668335, "success": true, "error": null}, {"index": 6, "question": "对于人民币理财产品，不同产品期限含义的产品占比分别是多少？", "extracted_keywords": {"产品期限": ["现金类", "日开", "7天", "14天", "1M", "2M", "3M", "6M", "9M", "1Y", "1.5Y", "2Y", "3Y", "3Y以上"], "指标名": ["占比"]}, "model_output": "```json\n{\n  \"产品期限\": [\"现金类\", \"日开\", \"7天\", \"14天\", \"1M\", \"2M\", \"3M\", \"6M\", \"9M\", \"1Y\", \"1.5Y\", \"2Y\", \"3Y\", \"3Y以上\"],\n  \"指标名\": [\"占比\"]\n}\n```", "processing_time": 2.135479211807251, "success": true, "error": null}, {"index": 7, "question": "20240224，对公和零售渠道下，产品至顺1号69期和新启航一年定开24号的持仓份额和持仓规模分别是多少？", "extracted_keywords": {"数据日期": ["20240224-20240224"], "渠道一级分类": ["对公", "零售"], "产品简称": ["至顺1号69期", "新启航一年定开24号"], "指标名": ["持仓份额", "持仓规模"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20240224-20240224\"],\n  \"渠道一级分类\": [\"对公\", \"零售\"],\n  \"产品简称\": [\"至顺1号69期\", \"新启航一年定开24号\"],\n  \"指标名\": [\"持仓份额\", \"持仓规模\"]\n}\n```", "processing_time": 6.127125978469849, "success": true, "error": null}, {"index": 8, "question": "20240224，零售渠道的产品，按照产品和币种维度下的持仓规模是多少？？", "extracted_keywords": {"数据日期": ["20240224-20240224"], "渠道一级分类": ["零售"], "指标名": ["持仓规模"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20240224-20240224\"],\n  \"渠道一级分类\": [\"零售\"],\n  \"指标名\": [\"持仓规模\"]\n}\n```", "processing_time": 2.105255126953125, "success": true, "error": null}, {"index": 9, "question": "20240201，零售渠道下，人民币中高风险净值型产品，业绩基准和持仓规模是多少？", "extracted_keywords": {"数据日期": ["20240201-20240201"], "渠道一级分类": ["零售"], "风险评级": ["R4级 (中高风险)"], "产品收益类型": ["净值"], "指标名": ["业绩基准", "持仓规模"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20240201-20240201\"],\n  \"渠道一级分类\": [\"零售\"],\n  \"风险评级\": [\"R4级 (中高风险)\"],\n  \"产品收益类型\": [\"净值\"],\n  \"指标名\": [\"业绩基准\", \"持仓规模\"]\n}\n```", "processing_time": 2.2628610134124756, "success": true, "error": null}]}