"""
平安理财ChatBI问答系统核心功能
实现问题识别、关键词提取、模板匹配和回填
"""

import json
import os
import re
import time
import logging
import dashscope
from dashscope import Generation
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi_core.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ChatBI-Core")

# 设置API密钥
API_KEY = 'sk-4af3f43ae9a74f0ebed6736b453a47c6'
dashscope.api_key = API_KEY

# 设置请求超时和重试次数
REQUEST_TIMEOUT = 30  # 秒
MAX_RETRIES = 3

def load_file(filename):
    """
    加载文件内容

    参数:
        filename (str): 文件路径

    返回:
        str/dict: 文件内容
    """
    try:
        logger.info(f"正在加载文件: {filename}")
        with open(filename, "r", encoding="utf-8") as f:
            if filename.endswith(".json"):
                content = json.load(f)
            else:
                content = f.read()
        logger.info(f"文件加载成功: {filename}")
        return content
    except Exception as e:
        logger.error(f"加载文件失败: {filename}, 错误: {str(e)}")
        return {} if filename.endswith(".json") else ""

def call_qwen_model(prompt, model="qwen3-30b-a3b", temperature=0.7, max_tokens=1500, caller=None):
    """
    调用千问模型并获取回复，包含重试机制

    参数:
        prompt (str): 提示文本
        model (str): 模型名称
        temperature (float): 控制输出随机性
        max_tokens (int): 最大生成token数
        caller (function): 调用者函数，用于记录模型输出

    返回:
        dict: 模型响应
    """
    for attempt in range(MAX_RETRIES):
        try:
            logger.info(f"调用千问模型 (尝试 {attempt+1}/{MAX_RETRIES})")

            # 实际调用API
            response = Generation.call(
                model=model,
                prompt=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                result_format='message',
                timeout=REQUEST_TIMEOUT,
                enable_thinking=False  # qwen3-30b-a3b模型必需参数
            )
            logger.info("模型调用成功")

            # 记录模型输出
            if caller and hasattr(response, 'output') and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
                model_output = response.output.choices[0].message.content
                setattr(caller, "last_model_output", model_output)

            return response
        except Exception as e:
            error_msg = str(e)
            logger.error(f"调用模型失败 (尝试 {attempt+1}/{MAX_RETRIES}): {error_msg}")

            if attempt < MAX_RETRIES - 1:
                # 指数退避重试
                wait_time = 2 ** attempt
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                # 所有重试都失败
                logger.error(f"达到最大重试次数，放弃请求")
                return {"error": error_msg}

def extract_keywords_zhibiao(user_question):
    """
    从用户问题中提取关键词

    参数:
        user_question (str): 用户问题

    返回:
        dict: 提取的关键词
    """
    logger.info(f"从用户问题中提取关键词: {user_question}")

    # 加载提示词模板
    prompt_template = """
# 平安理财ChatBI关键词提取

## 任务描述
你是平安理财ChatBI系统的关键词提取组件。你的任务是从用户问题中提取关键词，并将其映射到标准字段。

{knowledge_base}

## 输出格式
```json
{
  "字段名1": ["值1", "值2", ...],
  "字段名2": ["值1", "值2", ...],
  ...
}
```

## 提取规则
1. 从用户问题中提取指标，并映射到标准字段。仅需提取知识库包含的指标。
   注意，禁止提取知识库中不包含的指标名。
2. 识别问题中的指标名称，如"持仓规模"、"申购金额"等，保存在"指标名"字段中。
3. 输出JSON格式的结果，包含提取的关键词及其对应的标准字段。

现在，请分析以下用户问题，提取关键词：

用户问题: "{user_question}"

提取结果:

    """

    # 构建知识库部分的提示词
    knowledge_prompt = "## 知识库\n\n"

    knowledge_prompt += load_file("knowledge_bases/指标.txt")
    if not knowledge_prompt:
        logger.error("加载提取关键词提示词模板失败")
        return {}

    # 填充提示词模板
    prompt = prompt_template.replace("{knowledge_base}", knowledge_prompt)
    prompt = prompt.replace("{user_question}", user_question)
    # prompt = prompt.replace("{today}", datetime.now().strftime("%Y%m%d"))
    print(f"提示词:\n{prompt}")
    with open("prompt.txt", "w", encoding="utf-8") as f:
        f.write(prompt)

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=extract_keywords)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"提取关键词失败: {response['error']}")
        return {}

    try:
        # 检查响应格式并解析模型返回的JSON
        content = response.output.choices[0].message.content

        logger.debug(f"模型回复: {content}")

        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            logger.info(f"提取的关键词: {json.dumps(result, ensure_ascii=False)}")
            return result
        else:
            logger.warning("无法从模型响应中提取JSON")
            return {}
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")
        if hasattr(response, 'output'):
            logger.debug(f"output类型: {type(response.output)}")
        return {}


def extract_keywords_weidu(user_question):
    """
    从用户问题中提取关键词

    参数:
        user_question (str): 用户问题

    返回:
        dict: 提取的关键词
    """
    logger.info(f"从用户问题中提取关键词: {user_question}")

    # 加载提示词模板
    prompt_template = """
# 平安理财ChatBI关键词提取

## 任务描述
你是平安理财ChatBI系统的关键词提取组件。你的任务是从用户问题中提取维度关键词，并将其映射到标准字段。

{knowledge_base}

## 输出格式
```json
{
  "字段名1": ["值1", "值2", ...],
  "字段名2": ["值1", "值2", ...],
  ...
}
```

## 提取规则
1. 从用户问题中提取维度，并映射到标准字段。仅需提取知识库包含的维度。
   注意，禁止提取知识库中不包含的维度名。
2. 输出JSON格式的结果，包含提取的关键词及其对应的标准字段。

现在，请分析以下用户问题，提取关键词：

用户问题: "{user_question}"

提取结果:

    """

    # 构建知识库部分的提示词
    knowledge_prompt = "## 知识库\n\n"

    knowledge_prompt += load_file("knowledge_bases/指标.txt")
    if not knowledge_prompt:
        logger.error("加载提取关键词提示词模板失败")
        return {}

    # 填充提示词模板
    prompt = prompt_template.replace("{knowledge_base}", knowledge_prompt)
    prompt = prompt.replace("{user_question}", user_question)
    # prompt = prompt.replace("{today}", datetime.now().strftime("%Y%m%d"))
    print(f"提示词:\n{prompt}")
    with open("prompt.txt", "w", encoding="utf-8") as f:
        f.write(prompt)

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=extract_keywords)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"提取关键词失败: {response['error']}")
        return {}

    try:
        # 检查响应格式并解析模型返回的JSON
        content = response.output.choices[0].message.content

        logger.debug(f"模型回复: {content}")

        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            logger.info(f"提取的关键词: {json.dumps(result, ensure_ascii=False)}")
            return result
        else:
            logger.warning("无法从模型响应中提取JSON")
            return {}
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")
        if hasattr(response, 'output'):
            logger.debug(f"output类型: {type(response.output)}")
        return {}

def match_template(extracted_keywords):
    """
    根据提取的关键词匹配模板

    参数:
        extracted_keywords (dict): 提取的关键词

    返回:
        str: 匹配的模板
    """
    logger.info("根据提取的关键词匹配模板")

    # 加载模板
    templates = load_file("templates_new.json")
    if not templates:
        logger.error("加载模板失败")
        return ""

    logger.info(f"加载了 {len(templates)} 个模板")

    # 加载提示词模板
    prompt_template = load_file("prompts/template_matching_prompt.txt")
    if not prompt_template:
        logger.error("加载模板匹配提示词模板失败")
        return ""

    # 填充提示词模板
    prompt = prompt_template.replace("{extracted_keywords}", json.dumps(extracted_keywords, ensure_ascii=False))
    prompt = prompt.replace("{templates}", json.dumps(templates, ensure_ascii=False))

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=match_template)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"匹配模板失败: {response['error']}")
        return ""

    try:
        # 检查响应格式并获取模型返回的内容
        content = None
        if hasattr(response, 'output') and response.output and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
            content = response.output.choices[0].message.content
        elif hasattr(response, 'output') and response.output and hasattr(response.output, 'text'):
            content = response.output.text
        else:
            logger.error(f"无法从响应中获取内容，响应结构: {type(response)}")
            return ""

        logger.info(f"模型返回内容: {content[:200]}...")

        # 尝试提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            try:
                result = json.loads(json_str)
                matched_template = result.get("matched_template", "")
                confidence = result.get("confidence", 0)
                logger.info(f"从JSON中提取到模板: {matched_template}, 置信度: {confidence}")

                # 验证提取的模板是否在模板库中
                if matched_template in templates:
                    logger.info(f"匹配的模板在模板库中: {matched_template}")
                    return matched_template
                else:
                    logger.warning(f"提取的模板不在模板库中: {matched_template}")
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败: {str(e)}, 尝试其他方法")

        # 如果无法从JSON中提取模板，尝试直接从内容中提取
        logger.info("尝试直接从内容中提取模板")
        for template in templates:
            if f'"{template}"' in content or f"'{template}'" in content:
                logger.info(f"从内容中直接提取到模板: {template}")
                return template

        # 尝试提取带有"matched_template"关键字的行
        matched_template_line = None
        for line in content.split('\n'):
            if "matched_template" in line:
                matched_template_line = line
                break

        if matched_template_line:
            logger.info(f"找到包含matched_template的行: {matched_template_line}")
            # 尝试从这行中提取模板
            for template in templates:
                if template in matched_template_line:
                    logger.info(f"从matched_template行中提取到模板: {template}")
                    return template

        # 不再使用简单匹配逻辑
        logger.warning("无法从模型响应中提取模板，返回空字符串")
        return ""
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")
        # 不再使用简单匹配逻辑
        logger.warning("解析模型响应失败，返回空字符串")
        return ""

def simple_template_matching(extracted_keywords, templates):
    """
    简单的模板匹配逻辑，当模型匹配失败时使用

    参数:
        extracted_keywords (dict): 提取的关键词
        templates (list): 模板列表

    返回:
        str: 匹配的模板
    """
    logger.info("使用简单匹配逻辑")

    # 检查是否有渠道名称和指标名
    has_channel = "渠道名称" in extracted_keywords
    has_product = "产品简称" in extracted_keywords

    # 获取指标名
    indicator = None
    if "指标名" in extracted_keywords and extracted_keywords["指标名"]:
        indicator = extracted_keywords["指标名"][0]

    # 根据关键词组合选择模板
    for template in templates:
        # 如果同时有产品简称、渠道名称和指标名
        if has_product and has_channel and indicator:
            if "{#产品简称#}" in template and "{#渠道名称#}" in template and indicator in template:
                logger.info(f"匹配到模板(产品+渠道+指标): {template}")
                return template

        # 如果有渠道名称和指标名是净申赎金额
        if has_channel and indicator == "净申赎金额" and "{#渠道名称#}" in template and "净申赎金额" in template:
            if has_product and "{#产品简称#}" in template:
                if "{#渠道名称#}" in template and "{#产品简称#}" in template:
                    logger.info(f"匹配到模板(产品+渠道+净申赎): {template}")
                    return template
            else:
                if "{#渠道名称#}" in template and "总净申赎金额" in template:
                    logger.info(f"匹配到模板(渠道+总净申赎): {template}")
                    return template

        # 如果有渠道名称和指标名是赎回金额
        elif has_channel and indicator == "赎回金额" and "{#渠道名称#}" in template and "赎回金额" in template:
            if has_product and "{#产品简称#}" in template:
                if "{#渠道名称#}" in template and "{#产品简称#}" in template:
                    logger.info(f"匹配到模板(产品+渠道+赎回): {template}")
                    return template
            else:
                if "{#渠道名称#}" in template and "总赎回金额" in template:
                    logger.info(f"匹配到模板(渠道+总赎回): {template}")
                    return template

        # 如果有渠道名称和指标名是持仓规模
        elif has_channel and indicator == "持仓规模" and "{#渠道名称#}" in template and "持仓规模" in template:
            if has_product and "{#产品简称#}" in template:
                if "{#渠道名称#}" in template and "{#产品简称#}" in template:
                    logger.info(f"匹配到模板(产品+渠道+持仓): {template}")
                    return template
            else:
                if "{#渠道名称#}" in template and "总持仓规模" in template:
                    logger.info(f"匹配到模板(渠道+总持仓): {template}")
                    return template

        # 如果有渠道名称但没有产品简称
        elif has_channel and not has_product and "{#渠道名称#}" in template:
            if indicator and indicator in template:
                logger.info(f"匹配到模板(渠道+指标): {template}")
                return template

        # 如果有产品简称但没有渠道名称
        elif has_product and not has_channel and "{#产品简称#}" in template:
            if indicator and indicator in template:
                logger.info(f"匹配到模板(产品+指标): {template}")
                return template

    # 如果没有找到匹配的模板，返回一个默认模板
    if indicator == "净申赎金额":
        if has_channel and has_product:
            default_template = "{#数据日期#}{#产品简称#}在{#渠道名称#}的净申赎金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_channel:
            default_template = "{#数据日期#}{#渠道名称#}的总净申赎金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_product:
            default_template = "{#数据日期#}{#产品简称#}的净申赎金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
    elif indicator == "赎回金额":
        if has_channel and has_product:
            default_template = "{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_channel:
            default_template = "{#数据日期#}{#渠道名称#}的总赎回金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_product:
            default_template = "{#数据日期#}{#产品简称#}的赎回金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
    elif indicator == "持仓规模":
        if has_channel and has_product:
            default_template = "{#数据日期#}{#产品简称#}在{#渠道名称#}的持仓规模是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_channel:
            default_template = "{#数据日期#}{#渠道名称#}的总持仓规模是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_product:
            default_template = "{#数据日期#}{#产品简称#}的持仓规模是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template

    # 最后的兜底
    logger.warning("没有找到匹配的模板，使用空字符串")
    return ""

def fill_template(matched_template, extracted_keywords):
    """
    将提取的关键词填充到模板中

    参数:
        matched_template (str): 匹配的模板
        extracted_keywords (dict): 提取的关键词

    返回:
        str: 填充后的模板
    """
    logger.info("将提取的关键词填充到模板中")
    logger.info(f"匹配的模板: {matched_template}")
    logger.info(f"提取的关键词: {json.dumps(extracted_keywords, ensure_ascii=False)}")

    # 首先尝试使用简单的字符串替换方法填充模板
    try:
        filled_template = matched_template
        for field, values in extracted_keywords.items():
            if values and len(values) > 0:
                placeholder = f"{{#{field}#}}"
                if placeholder in filled_template:
                    filled_template = filled_template.replace(placeholder, values[0])

        # 检查是否所有占位符都已替换
        if "{#" not in filled_template and "#}" not in filled_template:
            logger.info(f"使用简单替换方法填充模板成功: {filled_template}")
            return filled_template
        else:
            logger.info("简单替换方法未能替换所有占位符，尝试使用模型填充")
    except Exception as e:
        logger.error(f"简单替换方法填充模板失败: {str(e)}")

    # 如果简单替换方法失败，使用模型填充
    # 加载提示词模板
    prompt_template = load_file("prompts/template_filling_prompt.txt")
    if not prompt_template:
        logger.error("加载模板填充提示词模板失败")
        return ""

    # 填充提示词模板
    prompt = prompt_template.replace("{extracted_keywords}", json.dumps(extracted_keywords, ensure_ascii=False))
    prompt = prompt.replace("{matched_template}", matched_template)

    # 添加明确的指示
    prompt += f"\n\n注意：请严格使用提供的模板 '{matched_template}'，不要修改模板结构，只替换占位符。"

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=fill_template)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"填充模板失败: {response['error']}")
        return ""

    try:
        # 检查响应格式并获取模型返回的文本
        content = None
        if hasattr(response, 'output') and response.output and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
            content = response.output.choices[0].message.content
        elif hasattr(response, 'output') and response.output and hasattr(response.output, 'text'):
            content = response.output.text
        else:
            logger.error(f"无法从响应中获取内容，响应结构: {type(response)}")
            # 回退到简单替换方法
            filled_template = matched_template
            for field, values in extracted_keywords.items():
                if values and len(values) > 0:
                    placeholder = f"{{#{field}#}}"
                    if placeholder in filled_template:
                        filled_template = filled_template.replace(placeholder, values[0])
            logger.info(f"回退：使用简单替换方法填充模板: {filled_template}")
            return filled_template

        # 提取填充结果
        result_start = content.rfind("填充结果:") + len("填充结果:")
        result = content[result_start:].strip()
        # 去掉可能的代码块标记
        if result.startswith("```") and result.endswith("```"):
            result = result[3:-3].strip()

        # 验证填充结果是否基于正确的模板
        # 检查填充结果是否与匹配的模板结构一致
        template_structure = re.sub(r'\{#.*?#\}', 'PLACEHOLDER', matched_template)
        result_structure = re.sub(r'[0-9a-zA-Z一-龥]+', 'PLACEHOLDER', result)
        if template_structure != result_structure:
            logger.warning(f"填充结果与模板结构不一致，尝试使用简单替换方法")
            # 回退到简单替换方法
            filled_template = matched_template
            for field, values in extracted_keywords.items():
                if values and len(values) > 0:
                    placeholder = f"{{#{field}#}}"
                    if placeholder in filled_template:
                        filled_template = filled_template.replace(placeholder, values[0])
            logger.info(f"使用简单替换方法填充模板: {filled_template}")
            return filled_template

        logger.info(f"填充后的模板: {result}")
        return result
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")

        # 出错时回退到简单替换方法
        try:
            filled_template = matched_template
            for field, values in extracted_keywords.items():
                if values and len(values) > 0:
                    placeholder = f"{{#{field}#}}"
                    if placeholder in filled_template:
                        filled_template = filled_template.replace(placeholder, values[0])
            logger.info(f"出错回退：使用简单替换方法填充模板: {filled_template}")
            return filled_template
        except Exception as e2:
            logger.error(f"简单替换方法也失败: {str(e2)}")
            return ""

def process_question(user_question):
    """
    处理用户问题

    参数:
        user_question (str): 用户问题

    返回:
        dict: 处理结果
    """
    logger.info(f"处理用户问题: {user_question}")

    start_time = time.time()
    model_outputs = []

    # 1. 提取关键词
    extracted_keywords, extraction_output = extract_keywords_with_output(user_question)
    print(f"提取关键词: {extracted_keywords}")
    print(f"提取关键词输出: {extraction_output}")
    if extraction_output:
        model_outputs.append(f"【关键词提取】\n{extraction_output}")

    if not extracted_keywords:
        logger.error("提取关键词失败")
        return {
            "success": False,
            "error": "提取关键词失败",
            "processing_time": time.time() - start_time,
            "model_output": "\n\n".join(model_outputs) if model_outputs else None
        }

    # 2. 匹配模板
    matched_template, matching_output = match_template_with_output(extracted_keywords)
    if matching_output:
        model_outputs.append(f"【模板匹配】\n{matching_output}")

    if not matched_template:
        logger.error("匹配模板失败")
        # 即使匹配失败，也返回大模型的输出
        error_result = {
            "success": False,
            "error": "匹配模板失败",
            "extracted_keywords": extracted_keywords,
            "processing_time": time.time() - start_time,
            "model_output": "\n\n".join(model_outputs) if model_outputs else None
        }

        # 记录详细的错误信息
        logger.info(f"处理失败，返回结果: {json.dumps(error_result, ensure_ascii=False)}")
        return error_result

    # 3. 填充模板
    filled_template, filling_output = fill_template_with_output(matched_template, extracted_keywords)
    if filling_output:
        model_outputs.append(f"【模板填充】\n{filling_output}")

    if not filled_template:
        logger.error("填充模板失败")
        # 即使填充失败，也返回大模型的输出
        error_result = {
            "success": False,
            "error": "填充模板失败",
            "extracted_keywords": extracted_keywords,
            "matched_template": matched_template,
            "processing_time": time.time() - start_time,
            "model_output": "\n\n".join(model_outputs) if model_outputs else None
        }

        # 记录详细的错误信息
        logger.info(f"处理失败，返回结果: {json.dumps(error_result, ensure_ascii=False)}")
        return error_result

    # 4. 返回结果
    processing_time = time.time() - start_time
    logger.info(f"处理完成，耗时: {processing_time:.2f}秒")

    return {
        "success": True,
        "user_question": user_question,
        "extracted_keywords": extracted_keywords,
        "matched_template": matched_template,
        "filled_template": filled_template,
        "processing_time": processing_time,
        "model_output": "\n\n".join(model_outputs) if model_outputs else None
    }

def extract_keywords_with_output(user_question):
    """
    从用户问题中提取关键词，并返回大模型输出

    参数:
        user_question (str): 用户问题

    返回:
        tuple: (提取的关键词, 大模型输出)
    """
    # 调用原始函数
    keywords_zhibiao = extract_keywords_zhibiao(user_question)
    keywords_weidu = extract_keywords_weidu(user_question)
    
    # 获取最后一次大模型输出
    model_output = None

    return keywords, model_output

def match_template_with_output(extracted_keywords):
    """
    根据提取的关键词匹配模板，并返回大模型输出

    参数:
        extracted_keywords (dict): 提取的关键词

    返回:
        tuple: (匹配的模板, 大模型输出)
    """
    # 调用原始函数
    template = match_template(extracted_keywords)

    # 获取最后一次大模型输出
    model_output = None
    if hasattr(match_template, "last_model_output"):
        model_output = match_template.last_model_output
        logger.info(f"获取到模板匹配的大模型输出: {model_output[:100]}...")
    else:
        logger.warning("未获取到模板匹配的大模型输出")

    return template, model_output

def fill_template_with_output(matched_template, extracted_keywords):
    """
    将提取的关键词填充到模板中，并返回大模型输出

    参数:
        matched_template (str): 匹配的模板
        extracted_keywords (dict): 提取的关键词

    返回:
        tuple: (填充后的模板, 大模型输出)
    """
    # 调用原始函数
    filled = fill_template(matched_template, extracted_keywords)

    # 获取最后一次大模型输出
    model_output = None
    if hasattr(fill_template, "last_model_output"):
        model_output = fill_template.last_model_output
        logger.info(f"获取到模板填充的大模型输出: {model_output[:100]}...")
    else:
        logger.warning("未获取到模板填充的大模型输出")

    return filled, model_output



# 测试函数
def test():
    """
    测试函数
    """
    test_question = "昨天天天成长3号在小招的赎回金额？"
    logger.info(f"测试问题: {test_question}")

    result = process_question(test_question)

    if result["success"]:
        print("\n" + "=" * 50)
        print(f"用户问题: {result['user_question']}")
        print(f"提取关键词: {json.dumps(result['extracted_keywords'], ensure_ascii=False)}")
        print(f"匹配模板: {result['matched_template']}")
        print(f"回填模板: {result['filled_template']}")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print(f"处理失败: {result['error']}")
        print("=" * 50)

if __name__ == "__main__":
    test()
