"""
平安理财ChatBI问答系统核心功能
实现问题识别、关键词提取、模板匹配和回填
"""

import json
import os
import re
import time
import logging
import dashscope
from dashscope import Generation
from datetime import datetime, timedelta
import pandas as pd
import concurrent.futures
import threading

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("chatbi_core.log", encoding="utf-8"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("ChatBI-Core")

# 设置API密钥
API_KEY = 'sk-4af3f43ae9a74f0ebed6736b453a47c6'
dashscope.api_key = API_KEY

# 设置请求超时和重试次数
REQUEST_TIMEOUT = 30  # 秒
MAX_RETRIES = 3

def load_file(filename):
    """
    加载文件内容

    参数:
        filename (str): 文件路径

    返回:
        str/dict: 文件内容
    """
    try:
        logger.info(f"正在加载文件: {filename}")
        with open(filename, "r", encoding="utf-8") as f:
            if filename.endswith(".json"):
                content = json.load(f)
            else:
                content = f.read()
        logger.info(f"文件加载成功: {filename}")
        return content
    except Exception as e:
        logger.error(f"加载文件失败: {filename}, 错误: {str(e)}")
        return {} if filename.endswith(".json") else ""

def call_qwen_model(prompt, model="qwen3-30b-a3b", temperature=0.7, max_tokens=1500, caller=None):
    """
    调用千问模型并获取回复，包含重试机制

    参数:
        prompt (str): 提示文本
        model (str): 模型名称
        temperature (float): 控制输出随机性
        max_tokens (int): 最大生成token数
        caller (function): 调用者函数，用于记录模型输出

    返回:
        dict: 模型响应
    """
    for attempt in range(MAX_RETRIES):
        try:
            logger.info(f"调用千问模型 (尝试 {attempt+1}/{MAX_RETRIES})")

            # 实际调用API
            response = Generation.call(
                model=model,
                prompt=prompt,
                temperature=temperature,
                max_tokens=max_tokens,
                result_format='message',
                timeout=REQUEST_TIMEOUT,
                enable_thinking=False  # qwen3-30b-a3b模型必需参数
            )
            logger.info("模型调用成功")

            # 记录模型输出
            if caller and hasattr(response, 'output') and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
                model_output = response.output.choices[0].message.content
                setattr(caller, "last_model_output", model_output)

            return response
        except Exception as e:
            error_msg = str(e)
            logger.error(f"调用模型失败 (尝试 {attempt+1}/{MAX_RETRIES}): {error_msg}")

            if attempt < MAX_RETRIES - 1:
                # 指数退避重试
                wait_time = 2 ** attempt
                logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                # 所有重试都失败
                logger.error(f"达到最大重试次数，放弃请求")
                return {"error": error_msg}

def extract_keywords_zhibiao(user_question):
    """
    从用户问题中提取关键词

    参数:
        user_question (str): 用户问题

    返回:
        dict: 提取的关键词
    """
    logger.info(f"从用户问题中提取关键词: {user_question}")

    # 加载提示词模板
    prompt_template = load_file("prompt_zhibiao.txt")

    # 构建知识库部分的提示词
    knowledge_prompt = "## 知识库\n\n"

    knowledge_prompt += load_file("knowledge_bases/指标.txt")
    if not knowledge_prompt:
        logger.error("加载提取关键词提示词模板失败")
        return {}

    # 填充提示词模板
    prompt = prompt_template.replace("{knowledge_base}", knowledge_prompt)
    prompt = prompt.replace("{user_question}", user_question)
    # prompt = prompt.replace("{today}", datetime.now().strftime("%Y%m%d"))
    # print(f"提示词:\n{prompt}")
    # with open("prompt_zhibiao.txt", "w", encoding="utf-8") as f:
    #     f.write(prompt)

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=extract_keywords_zhibiao)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"提取关键词失败: {response['error']}")
        return {}

    try:
        # 检查响应格式并解析模型返回的JSON
        content = response.output.choices[0].message.content

        logger.debug(f"模型回复: {content}")

        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            logger.info(f"提取的关键词: {json.dumps(result, ensure_ascii=False)}")
            return result
        else:
            logger.warning("无法从模型响应中提取JSON")
            return {}
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")
        if hasattr(response, 'output'):
            logger.debug(f"output类型: {type(response.output)}")
        return {}


def extract_keywords_weidu(user_question):
    """
    从用户问题中提取关键词

    参数:
        user_question (str): 用户问题

    返回:
        dict: 提取的关键词
    """
    logger.info(f"从用户问题中提取关键词: {user_question}")

    # 加载提示词模板
    prompt_template = load_file("prompt_weidu.txt")

    # 构建知识库部分的提示词
    knowledge_prompt = "## 知识库\n\n"

    knowledge_prompt += load_file("knowledge_bases/维度.txt")
    if not knowledge_prompt:
        logger.error("加载提取关键词提示词模板失败")
        return {}

    # 填充提示词模板
    prompt = prompt_template.replace("{knowledge_base}", knowledge_prompt)
    prompt = prompt.replace("{user_question}", user_question)
    # prompt = prompt.replace("{today}", datetime.now().strftime("%Y%m%d"))
    # print(f"提示词:\n{prompt}")
    # with open("prompt_weidu.txt", "w", encoding="utf-8") as f:
    #     f.write(prompt)

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=extract_keywords_weidu)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"提取关键词失败: {response['error']}")
        return {}

    try:
        # 检查响应格式并解析模型返回的JSON
        content = response.output.choices[0].message.content

        logger.debug(f"模型回复: {content}")

        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            logger.info(f"提取的关键词: {json.dumps(result, ensure_ascii=False)}")
            return result
        else:
            logger.warning("无法从模型响应中提取JSON")
            return {}
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")
        if hasattr(response, 'output'):
            logger.debug(f"output类型: {type(response.output)}")
        return {}

def extract_keywords_time(user_question):
    """
    从用户问题中提取关键词

    参数:
        user_question (str): 用户问题

    返回:
        dict: 提取的关键词
    """
    logger.info(f"从用户问题中提取关键词: {user_question}")

    # 加载提示词模板
    prompt_template = """
# 平安理财ChatBI关键词提取

## 任务描述
你是平安理财ChatBI系统的时间提取组件。你的任务是从用户问题中提取时间信息，并将其映射到标准字段。

{knowledge_base}

## 输出格式
```json
{
  "字段名1": ["值1", "值2", ...],
  "字段名2": ["值1", "值2", ...],
  ...
}
```

## 提取规则
1. 处理时间表达式，如"昨天"、"今天"、"本月"等，转换为YYYYMMDD格式的具体日期。
   如果是时间段，则输出起止时间；如果是单个时间点，则起止时间相同。
   今天的时间为：{today}
   - 例如："昨天" → "20250514-20250514"（假设今天是2025年5月15日）
   - 例如："本季度内" → "20250401-20250630"（假设今天是2025年5月15日）

现在，请分析以下用户问题，提取关键词：

用户问题: "{user_question}"

提取结果:

    """

    # 构建知识库部分的提示词
    knowledge_prompt = "## 知识库\n\n"

    knowledge_prompt += load_file("knowledge_bases/时间知识.txt")
    if not knowledge_prompt:
        logger.error("加载提取关键词提示词模板失败")
        return {}

    # 填充提示词模板
    prompt = prompt_template.replace("{knowledge_base}", knowledge_prompt)
    prompt = prompt.replace("{user_question}", user_question)
    prompt = prompt.replace("{today}", datetime.now().strftime("%Y%m%d"))
    # print(f"提示词:\n{prompt}")
    with open("prompt.txt", "w", encoding="utf-8") as f:
        f.write(prompt)

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=extract_keywords_time)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"提取关键词失败: {response['error']}")
        return {}

    try:
        # 检查响应格式并解析模型返回的JSON
        content = response.output.choices[0].message.content

        logger.debug(f"模型回复: {content}")

        # 提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            result = json.loads(json_str)
            logger.info(f"提取的关键词: {json.dumps(result, ensure_ascii=False)}")
            return result
        else:
            logger.warning("无法从模型响应中提取JSON")
            return {}
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")
        if hasattr(response, 'output'):
            logger.debug(f"output类型: {type(response.output)}")
        return {}

def match_template(extracted_keywords):
    """
    根据提取的关键词匹配模板

    参数:
        extracted_keywords (dict): 提取的关键词

    返回:
        str: 匹配的模板
    """
    logger.info("根据提取的关键词匹配模板")

    # 加载模板
    templates = load_file("templates_new.json")
    if not templates:
        logger.error("加载模板失败")
        return ""

    logger.info(f"加载了 {len(templates)} 个模板")

    # 加载提示词模板
    prompt_template = load_file("prompts/template_matching_prompt.txt")
    if not prompt_template:
        logger.error("加载模板匹配提示词模板失败")
        return ""

    # 填充提示词模板
    prompt = prompt_template.replace("{extracted_keywords}", json.dumps(extracted_keywords, ensure_ascii=False))
    prompt = prompt.replace("{templates}", json.dumps(templates, ensure_ascii=False))

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=match_template)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"匹配模板失败: {response['error']}")
        return ""

    try:
        # 检查响应格式并获取模型返回的内容
        content = None
        if hasattr(response, 'output') and response.output and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
            content = response.output.choices[0].message.content
        elif hasattr(response, 'output') and response.output and hasattr(response.output, 'text'):
            content = response.output.text
        else:
            logger.error(f"无法从响应中获取内容，响应结构: {type(response)}")
            return ""

        logger.info(f"模型返回内容: {content[:200]}...")

        # 尝试提取JSON部分
        json_start = content.find('{')
        json_end = content.rfind('}') + 1
        if json_start >= 0 and json_end > json_start:
            json_str = content[json_start:json_end]
            try:
                result = json.loads(json_str)
                matched_template = result.get("matched_template", "")
                confidence = result.get("confidence", 0)
                logger.info(f"从JSON中提取到模板: {matched_template}, 置信度: {confidence}")

                # 验证提取的模板是否在模板库中
                if matched_template in templates:
                    logger.info(f"匹配的模板在模板库中: {matched_template}")
                    return matched_template
                else:
                    logger.warning(f"提取的模板不在模板库中: {matched_template}")
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败: {str(e)}, 尝试其他方法")

        # 如果无法从JSON中提取模板，尝试直接从内容中提取
        logger.info("尝试直接从内容中提取模板")
        for template in templates:
            if f'"{template}"' in content or f"'{template}'" in content:
                logger.info(f"从内容中直接提取到模板: {template}")
                return template

        # 尝试提取带有"matched_template"关键字的行
        matched_template_line = None
        for line in content.split('\n'):
            if "matched_template" in line:
                matched_template_line = line
                break

        if matched_template_line:
            logger.info(f"找到包含matched_template的行: {matched_template_line}")
            # 尝试从这行中提取模板
            for template in templates:
                if template in matched_template_line:
                    logger.info(f"从matched_template行中提取到模板: {template}")
                    return template

        # 不再使用简单匹配逻辑
        logger.warning("无法从模型响应中提取模板，返回空字符串")
        return ""
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")
        # 不再使用简单匹配逻辑
        logger.warning("解析模型响应失败，返回空字符串")
        return ""

def simple_template_matching(extracted_keywords, templates):
    """
    简单的模板匹配逻辑，当模型匹配失败时使用

    参数:
        extracted_keywords (dict): 提取的关键词
        templates (list): 模板列表

    返回:
        str: 匹配的模板
    """
    logger.info("使用简单匹配逻辑")

    # 检查是否有渠道名称和指标名
    has_channel = "渠道名称" in extracted_keywords
    has_product = "产品简称" in extracted_keywords

    # 获取指标名
    indicator = None
    if "指标名" in extracted_keywords and extracted_keywords["指标名"]:
        indicator = extracted_keywords["指标名"][0]

    # 根据关键词组合选择模板
    for template in templates:
        # 如果同时有产品简称、渠道名称和指标名
        if has_product and has_channel and indicator:
            if "{#产品简称#}" in template and "{#渠道名称#}" in template and indicator in template:
                logger.info(f"匹配到模板(产品+渠道+指标): {template}")
                return template

        # 如果有渠道名称和指标名是净申赎金额
        if has_channel and indicator == "净申赎金额" and "{#渠道名称#}" in template and "净申赎金额" in template:
            if has_product and "{#产品简称#}" in template:
                if "{#渠道名称#}" in template and "{#产品简称#}" in template:
                    logger.info(f"匹配到模板(产品+渠道+净申赎): {template}")
                    return template
            else:
                if "{#渠道名称#}" in template and "总净申赎金额" in template:
                    logger.info(f"匹配到模板(渠道+总净申赎): {template}")
                    return template

        # 如果有渠道名称和指标名是赎回金额
        elif has_channel and indicator == "赎回金额" and "{#渠道名称#}" in template and "赎回金额" in template:
            if has_product and "{#产品简称#}" in template:
                if "{#渠道名称#}" in template and "{#产品简称#}" in template:
                    logger.info(f"匹配到模板(产品+渠道+赎回): {template}")
                    return template
            else:
                if "{#渠道名称#}" in template and "总赎回金额" in template:
                    logger.info(f"匹配到模板(渠道+总赎回): {template}")
                    return template

        # 如果有渠道名称和指标名是持仓规模
        elif has_channel and indicator == "持仓规模" and "{#渠道名称#}" in template and "持仓规模" in template:
            if has_product and "{#产品简称#}" in template:
                if "{#渠道名称#}" in template and "{#产品简称#}" in template:
                    logger.info(f"匹配到模板(产品+渠道+持仓): {template}")
                    return template
            else:
                if "{#渠道名称#}" in template and "总持仓规模" in template:
                    logger.info(f"匹配到模板(渠道+总持仓): {template}")
                    return template

        # 如果有渠道名称但没有产品简称
        elif has_channel and not has_product and "{#渠道名称#}" in template:
            if indicator and indicator in template:
                logger.info(f"匹配到模板(渠道+指标): {template}")
                return template

        # 如果有产品简称但没有渠道名称
        elif has_product and not has_channel and "{#产品简称#}" in template:
            if indicator and indicator in template:
                logger.info(f"匹配到模板(产品+指标): {template}")
                return template

    # 如果没有找到匹配的模板，返回一个默认模板
    if indicator == "净申赎金额":
        if has_channel and has_product:
            default_template = "{#数据日期#}{#产品简称#}在{#渠道名称#}的净申赎金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_channel:
            default_template = "{#数据日期#}{#渠道名称#}的总净申赎金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_product:
            default_template = "{#数据日期#}{#产品简称#}的净申赎金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
    elif indicator == "赎回金额":
        if has_channel and has_product:
            default_template = "{#数据日期#}{#产品简称#}在{#渠道名称#}的赎回金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_channel:
            default_template = "{#数据日期#}{#渠道名称#}的总赎回金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_product:
            default_template = "{#数据日期#}{#产品简称#}的赎回金额是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
    elif indicator == "持仓规模":
        if has_channel and has_product:
            default_template = "{#数据日期#}{#产品简称#}在{#渠道名称#}的持仓规模是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_channel:
            default_template = "{#数据日期#}{#渠道名称#}的总持仓规模是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template
        elif has_product:
            default_template = "{#数据日期#}{#产品简称#}的持仓规模是多少?"
            logger.info(f"使用默认模板: {default_template}")
            return default_template

    # 最后的兜底
    logger.warning("没有找到匹配的模板，使用空字符串")
    return ""

def fill_template(matched_template, extracted_keywords):
    """
    将提取的关键词填充到模板中

    参数:
        matched_template (str): 匹配的模板
        extracted_keywords (dict): 提取的关键词

    返回:
        str: 填充后的模板
    """
    logger.info("将提取的关键词填充到模板中")
    logger.info(f"匹配的模板: {matched_template}")
    logger.info(f"提取的关键词: {json.dumps(extracted_keywords, ensure_ascii=False)}")

    # 首先尝试使用简单的字符串替换方法填充模板
    try:
        filled_template = matched_template
        for field, values in extracted_keywords.items():
            if values and len(values) > 0:
                placeholder = f"{{#{field}#}}"
                if placeholder in filled_template:
                    filled_template = filled_template.replace(placeholder, values[0])

        # 检查是否所有占位符都已替换
        if "{#" not in filled_template and "#}" not in filled_template:
            logger.info(f"使用简单替换方法填充模板成功: {filled_template}")
            return filled_template
        else:
            logger.info("简单替换方法未能替换所有占位符，尝试使用模型填充")
    except Exception as e:
        logger.error(f"简单替换方法填充模板失败: {str(e)}")

    # 如果简单替换方法失败，使用模型填充
    # 加载提示词模板
    prompt_template = load_file("prompts/template_filling_prompt.txt")
    if not prompt_template:
        logger.error("加载模板填充提示词模板失败")
        return ""

    # 填充提示词模板
    prompt = prompt_template.replace("{extracted_keywords}", json.dumps(extracted_keywords, ensure_ascii=False))
    prompt = prompt.replace("{matched_template}", matched_template)

    # 添加明确的指示
    prompt += f"\n\n注意：请严格使用提供的模板 '{matched_template}'，不要修改模板结构，只替换占位符。"

    # 调用模型
    response = call_qwen_model(prompt, temperature=0.3, caller=fill_template)

    if isinstance(response, dict) and "error" in response:
        logger.error(f"填充模板失败: {response['error']}")
        return ""

    try:
        # 检查响应格式并获取模型返回的文本
        content = None
        if hasattr(response, 'output') and response.output and hasattr(response.output, 'choices') and len(response.output.choices) > 0:
            content = response.output.choices[0].message.content
        elif hasattr(response, 'output') and response.output and hasattr(response.output, 'text'):
            content = response.output.text
        else:
            logger.error(f"无法从响应中获取内容，响应结构: {type(response)}")
            # 回退到简单替换方法
            filled_template = matched_template
            for field, values in extracted_keywords.items():
                if values and len(values) > 0:
                    placeholder = f"{{#{field}#}}"
                    if placeholder in filled_template:
                        filled_template = filled_template.replace(placeholder, values[0])
            logger.info(f"回退：使用简单替换方法填充模板: {filled_template}")
            return filled_template

        # 提取填充结果
        result_start = content.rfind("填充结果:") + len("填充结果:")
        result = content[result_start:].strip()
        # 去掉可能的代码块标记
        if result.startswith("```") and result.endswith("```"):
            result = result[3:-3].strip()

        # 验证填充结果是否基于正确的模板
        # 检查填充结果是否与匹配的模板结构一致
        template_structure = re.sub(r'\{#.*?#\}', 'PLACEHOLDER', matched_template)
        result_structure = re.sub(r'[0-9a-zA-Z一-龥]+', 'PLACEHOLDER', result)
        if template_structure != result_structure:
            logger.warning(f"填充结果与模板结构不一致，尝试使用简单替换方法")
            # 回退到简单替换方法
            filled_template = matched_template
            for field, values in extracted_keywords.items():
                if values and len(values) > 0:
                    placeholder = f"{{#{field}#}}"
                    if placeholder in filled_template:
                        filled_template = filled_template.replace(placeholder, values[0])
            logger.info(f"使用简单替换方法填充模板: {filled_template}")
            return filled_template

        logger.info(f"填充后的模板: {result}")
        return result
    except Exception as e:
        logger.error(f"解析模型响应失败: {str(e)}")
        logger.debug(f"响应类型: {type(response)}")

        # 出错时回退到简单替换方法
        try:
            filled_template = matched_template
            for field, values in extracted_keywords.items():
                if values and len(values) > 0:
                    placeholder = f"{{#{field}#}}"
                    if placeholder in filled_template:
                        filled_template = filled_template.replace(placeholder, values[0])
            logger.info(f"出错回退：使用简单替换方法填充模板: {filled_template}")
            return filled_template
        except Exception as e2:
            logger.error(f"简单替换方法也失败: {str(e2)}")
            return ""

def process_question(user_question):
    """
    处理用户问题

    参数:
        user_question (str): 用户问题

    返回:
        dict: 处理结果
    """
    logger.info(f"处理用户问题: {user_question}")

    start_time = time.time()
    model_outputs = []

    # 1. 提取关键词
    extracted_keywords, extraction_output = extract_keywords_with_output(user_question)
    print(f"提取关键词: {extracted_keywords}")
    print(f"提取关键词输出: {extraction_output}")
    if extraction_output:
        model_outputs.append(f"【关键词提取】\n{extraction_output}")

    if not extracted_keywords:
        logger.error("提取关键词失败")
        return {
            "success": False,
            "error": "提取关键词失败",
            "processing_time": time.time() - start_time,
            "model_output": "\n\n".join(model_outputs) if model_outputs else None
        }

    # 2. 匹配模板
    matched_template, matching_output = match_template_with_output(extracted_keywords)
    if matching_output:
        model_outputs.append(f"【模板匹配】\n{matching_output}")

    if not matched_template:
        logger.error("匹配模板失败")
        # 即使匹配失败，也返回大模型的输出
        error_result = {
            "success": False,
            "error": "匹配模板失败",
            "extracted_keywords": extracted_keywords,
            "processing_time": time.time() - start_time,
            "model_output": "\n\n".join(model_outputs) if model_outputs else None
        }

        # 记录详细的错误信息
        logger.info(f"处理失败，返回结果: {json.dumps(error_result, ensure_ascii=False)}")
        return error_result

    # 3. 填充模板
    filled_template, filling_output = fill_template_with_output(matched_template, extracted_keywords)
    if filling_output:
        model_outputs.append(f"【模板填充】\n{filling_output}")

    if not filled_template:
        logger.error("填充模板失败")
        # 即使填充失败，也返回大模型的输出
        error_result = {
            "success": False,
            "error": "填充模板失败",
            "extracted_keywords": extracted_keywords,
            "matched_template": matched_template,
            "processing_time": time.time() - start_time,
            "model_output": "\n\n".join(model_outputs) if model_outputs else None
        }

        # 记录详细的错误信息
        logger.info(f"处理失败，返回结果: {json.dumps(error_result, ensure_ascii=False)}")
        return error_result

    # 4. 返回结果
    processing_time = time.time() - start_time
    logger.info(f"处理完成，耗时: {processing_time:.2f}秒")

    return {
        "success": True,
        "user_question": user_question,
        "extracted_keywords": extracted_keywords,
        "matched_template": matched_template,
        "filled_template": filled_template,
        "processing_time": processing_time,
        "model_output": "\n\n".join(model_outputs) if model_outputs else None
    }

def extract_keywords_with_output(user_question):
    """
    从用户问题中提取关键词（并发调用指标、维度和时间提取），并返回大模型输出

    参数:
        user_question (str): 用户问题

    返回:
        tuple: (提取的关键词, 大模型输出)
    """
    logger.info(f"开始并发提取关键词: {user_question}")

    # 使用线程池并发调用指标、维度和时间提取
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        # 提交三个任务
        future_zhibiao = executor.submit(extract_keywords_zhibiao, user_question)
        future_weidu = executor.submit(extract_keywords_weidu, user_question)
        future_time = executor.submit(extract_keywords_time, user_question)

        # 等待三个任务完成
        try:
            keywords_zhibiao = future_zhibiao.result(timeout=60)  # 60秒超时
            keywords_weidu = future_weidu.result(timeout=60)     # 60秒超时
            keywords_time = future_time.result(timeout=60)       # 60秒超时
        except concurrent.futures.TimeoutError:
            logger.error("关键词提取超时")
            return {}, None
        except Exception as e:
            logger.error(f"并发提取关键词失败: {str(e)}")
            return {}, None

    # 合并三个结果
    keywords = {}

    # 定义合并函数
    def merge_keywords(target_dict, source_dict, source_name):
        """合并关键词字典"""
        if source_dict:
            for key, value in source_dict.items():
                if key in target_dict:
                    # 如果键已存在，合并值列表
                    if isinstance(target_dict[key], list) and isinstance(value, list):
                        target_dict[key].extend(value)
                        # 去重但保持顺序
                        target_dict[key] = list(dict.fromkeys(target_dict[key]))
                    else:
                        target_dict[key] = value
                else:
                    target_dict[key] = value
            logger.debug(f"合并{source_name}关键词: {len(source_dict)} 个字段")

    # 添加指标关键词
    merge_keywords(keywords, keywords_zhibiao, "指标")

    # 添加维度关键词
    merge_keywords(keywords, keywords_weidu, "维度")

    # 添加时间关键词
    merge_keywords(keywords, keywords_time, "时间")

    logger.info(f"并发提取完成，指标关键词: {len(keywords_zhibiao) if keywords_zhibiao else 0} 个字段")
    logger.info(f"并发提取完成，维度关键词: {len(keywords_weidu) if keywords_weidu else 0} 个字段")
    logger.info(f"并发提取完成，时间关键词: {len(keywords_time) if keywords_time else 0} 个字段")
    logger.info(f"合并后关键词: {len(keywords)} 个字段")
    logger.info(f"最终关键词: {json.dumps(keywords, ensure_ascii=False)}")

    # 获取最后一次大模型输出（合并三个并发调用的输出）
    model_output = None
    outputs = []

    if hasattr(extract_keywords_zhibiao, "last_model_output") and extract_keywords_zhibiao.last_model_output:
        outputs.append(f"【指标提取】\n{extract_keywords_zhibiao.last_model_output}")

    if hasattr(extract_keywords_weidu, "last_model_output") and extract_keywords_weidu.last_model_output:
        outputs.append(f"【维度提取】\n{extract_keywords_weidu.last_model_output}")

    if hasattr(extract_keywords_time, "last_model_output") and extract_keywords_time.last_model_output:
        outputs.append(f"【时间提取】\n{extract_keywords_time.last_model_output}")

    model_output = "\n\n".join(outputs) if outputs else None

    return keywords, model_output

def match_template_with_output(extracted_keywords):
    """
    根据提取的关键词匹配模板，并返回大模型输出

    参数:
        extracted_keywords (dict): 提取的关键词

    返回:
        tuple: (匹配的模板, 大模型输出)
    """
    # 调用原始函数
    template = match_template(extracted_keywords)

    # 获取最后一次大模型输出
    model_output = None
    if hasattr(match_template, "last_model_output"):
        model_output = match_template.last_model_output
        logger.info(f"获取到模板匹配的大模型输出: {model_output[:100]}...")
    else:
        logger.warning("未获取到模板匹配的大模型输出")

    return template, model_output

def fill_template_with_output(matched_template, extracted_keywords):
    """
    将提取的关键词填充到模板中，并返回大模型输出

    参数:
        matched_template (str): 匹配的模板
        extracted_keywords (dict): 提取的关键词

    返回:
        tuple: (填充后的模板, 大模型输出)
    """
    # 调用原始函数
    filled = fill_template(matched_template, extracted_keywords)

    # 获取最后一次大模型输出
    model_output = None
    if hasattr(fill_template, "last_model_output"):
        model_output = fill_template.last_model_output
        logger.info(f"获取到模板填充的大模型输出: {model_output[:100]}...")
    else:
        logger.warning("未获取到模板填充的大模型输出")

    return filled, model_output



# 测试函数
def test():
    """
    测试函数
    """
    test_question = "昨天天天成长3号在小招的赎回金额？"
    logger.info(f"测试问题: {test_question}")

    result = process_question(test_question)

    if result["success"]:
        print("\n" + "=" * 50)
        print(f"用户问题: {result['user_question']}")
        print(f"提取关键词: {json.dumps(result['extracted_keywords'], ensure_ascii=False)}")
        print(f"匹配模板: {result['matched_template']}")
        print(f"回填模板: {result['filled_template']}")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print(f"处理失败: {result['error']}")
        print("=" * 50)

# ==================== 用户问题改写模块 ====================

class QuestionRewriter:
    """用户问题改写器，将别称替换为标准名称"""

    def __init__(self, slang_file="knowledge_bases/黑话.xlsx"):
        """
        初始化问题改写器

        参数:
            slang_file (str): 黑话文件路径
        """
        self.slang_file = slang_file
        self.slang_dict = {}
        self.load_slang_dict()

    def load_slang_dict(self):
        """加载黑话字典"""
        try:
            logger.info(f"正在加载黑话文件: {self.slang_file}")

            # 读取Excel文件
            df = pd.read_excel(self.slang_file)
            logger.info(f"成功加载黑话文件，共 {len(df)} 行数据")
            logger.info(f"列名: {list(df.columns)}")

            # 检查文件格式
            if len(df.columns) < 2:
                logger.error("黑话文件格式不正确，需要至少2列")
                return

            # 根据列名确定别称列和标准名称列
            if "别称" in df.columns and "标准名称" in df.columns:
                alias_col = "别称"  # 别称列
                standard_col = "标准名称"  # 标准名称列
            else:
                # 如果列名不标准，假设第二列是别称，第一列是标准名称
                alias_col = df.columns[1]  # 别称列
                standard_col = df.columns[0]  # 标准名称列

            logger.info(f"使用列: 别称列='{alias_col}', 标准名称列='{standard_col}'")

            # 构建别称到标准名称的映射字典
            for index, row in df.iterrows():
                alias = row[alias_col]
                standard = row[standard_col]

                # 跳过空值
                if pd.isna(alias) or pd.isna(standard):
                    continue

                alias = str(alias).strip()
                standard = str(standard).strip()

                if alias and standard:
                    self.slang_dict[alias] = standard

            logger.info(f"构建黑话字典完成，共 {len(self.slang_dict)} 个映射")

            # 显示前几个映射示例
            sample_items = list(self.slang_dict.items())[:5]
            for alias, standard in sample_items:
                logger.info(f"  {alias} → {standard}")

        except Exception as e:
            logger.error(f"加载黑话文件失败: {str(e)}")

    def preprocess_text(self, text):
        """预处理文本，去除多余空格"""
        if not text:
            return ""
        return re.sub(r'\s+', '', text)

    def find_longest_alias_match(self, question):
        """
        在问题中查找最长的别称匹配

        参数:
            question (str): 用户问题

        返回:
            tuple: (匹配的别称, 对应的标准名称, 匹配位置) 或 (None, None, None)
        """
        if not question or not self.slang_dict:
            return None, None, None

        # 预处理问题文本
        processed_question = self.preprocess_text(question)

        # 按别称长度降序排序，优先匹配更长的别称
        sorted_aliases = sorted(self.slang_dict.keys(), key=len, reverse=True)

        for alias in sorted_aliases:
            processed_alias = self.preprocess_text(alias)

            if processed_alias and processed_alias in processed_question:
                # 找到匹配位置
                start_pos = question.find(alias)
                if start_pos == -1:
                    # 如果直接查找失败，尝试忽略空格查找
                    for i in range(len(question) - len(alias) + 1):
                        substr = question[i:i+len(alias)]
                        if self.preprocess_text(substr) == processed_alias:
                            start_pos = i
                            break

                if start_pos >= 0:
                    standard_name = self.slang_dict[alias]
                    logger.info(f"找到最长匹配: '{alias}' → '{standard_name}' (位置: {start_pos})")
                    return alias, standard_name, start_pos

        return None, None, None

    def rewrite_question(self, question):
        """
        改写用户问题，将别称替换为标准名称

        参数:
            question (str): 原始用户问题

        返回:
            dict: 包含改写结果的字典
        """
        if not question:
            return {
                'original_question': question,
                'rewritten_question': question,
                'replacements': [],
                'has_changes': False
            }

        logger.info(f"开始改写问题: {question}")

        rewritten_question = question
        replacements = []

        # 查找最长匹配的别称
        alias, standard_name, position = self.find_longest_alias_match(question)

        if alias and standard_name:
            # 执行替换
            rewritten_question = question.replace(alias, standard_name, 1)  # 只替换第一个匹配

            replacements.append({
                'alias': alias,
                'standard_name': standard_name,
                'position': position
            })

            logger.info(f"问题改写完成: '{question}' → '{rewritten_question}'")
        else:
            logger.info("未找到需要替换的别称")

        result = {
            'original_question': question,
            'rewritten_question': rewritten_question,
            'replacements': replacements,
            'has_changes': len(replacements) > 0
        }

        return result

    def get_slang_dict(self):
        """获取黑话字典"""
        return self.slang_dict.copy()

    def search_alias(self, keyword):
        """
        搜索包含关键词的别称

        参数:
            keyword (str): 搜索关键词

        返回:
            list: 匹配的(别称, 标准名称)元组列表
        """
        results = []
        keyword_lower = keyword.lower()

        for alias, standard in self.slang_dict.items():
            if keyword_lower in alias.lower() or keyword_lower in standard.lower():
                results.append((alias, standard))

        return results

def rewrite_question_simple(question, slang_file="knowledge_bases/黑话.xlsx"):
    """
    简化的问题改写函数

    参数:
        question (str): 用户问题
        slang_file (str): 黑话文件路径

    返回:
        str: 改写后的问题
    """
    try:
        rewriter = QuestionRewriter(slang_file)
        result = rewriter.rewrite_question(question)
        return result['rewritten_question']
    except Exception as e:
        logger.error(f"问题改写失败: {str(e)}")
        return question

def test_question_rewriter():
    """测试问题改写功能"""
    print("🚀 测试问题改写功能")
    print("=" * 60)

    # 测试问题
    test_questions = [
        "我司产品在小招的规模是多少？",
        "平安理财产品在工行的持仓情况如何？",
        "这是一个没有别称的问题"
    ]

    try:
        # 创建改写器
        rewriter = QuestionRewriter()

        # 显示黑话字典摘要
        slang_dict = rewriter.get_slang_dict()
        print(f"📊 黑话字典摘要:")
        print(f"总映射数: {len(slang_dict)}")

        sample_items = list(slang_dict.items())[:10]
        print("前10个映射示例:")
        for alias, standard in sample_items:
            print(f"  {alias} → {standard}")

        print("\n" + "=" * 60)

        # 测试每个问题
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔍 测试问题 {i}: {question}")

            # 改写问题
            result = rewriter.rewrite_question(question)

            print(f"改写结果: {result['rewritten_question']}")
            print(f"是否有变化: {result['has_changes']}")

            if result['replacements']:
                print("替换详情:")
                for replacement in result['replacements']:
                    print(f"  位置 {replacement['position']}: '{replacement['alias']}' → '{replacement['standard_name']}'")

        print("\n" + "=" * 60)
        print("🎉 测试完成！")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    extract_keywords_with_output('昨天天天成长3号在小招的赎回金额？')  # 原有测试
    # test_question_rewriter()  # 新增测试
