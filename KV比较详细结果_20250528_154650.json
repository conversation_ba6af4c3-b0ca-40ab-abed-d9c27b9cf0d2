[{"row_index": 1, "actual_text": "{\n  \"数据日期\": [\n    \"20250501\"\n  ],\n  \"产品分类\": [\n    \"权益\"\n  ],\n  \"指标名\": [\n    \"预约申购金额\",\n    \"预约赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250501\"\n  ],\n  \"产品分类\": [\n    \"权益\"\n  ],\n  \"指标名\": [\n    \"预约申购金额\",\n    \"预约赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250501"], "产品分类": ["权益"], "指标名": ["预约申购金额", "预约赎回金额"]}, "expected_json": {"数据日期": ["20250501"], "产品分类": ["权益"], "指标名": ["预约申购金额", "预约赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 2, "actual_text": "{\n  \"数据日期\": [\n    \"20250513\",\n    \"20250514\",\n    \"20250515\"\n  ],\n  \"产品系列\": [\n    \"启航\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250513\",\n    \"20250514\",\n    \"20250515\"\n  ],\n  \"产品系列\": [\n    \"启航\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"数据日期": ["20250513", "20250514", "20250515"], "产品系列": ["启航"], "指标名": ["持仓客户数"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"数据日期": ["20250513", "20250514", "20250515"], "产品系列": ["启航"], "指标名": ["持仓客户数"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 3, "actual_text": "{\n  \"数据日期\": [\n    \"20250425\",\n    \"20250426\",\n    \"20250427\",\n    \"20250428\",\n    \"20250429\",\n    \"20250430\",\n    \"20250501\",\n    \"20250502\",\n    \"20250503\",\n    \"20250504\",\n    \"20250505\",\n    \"20250506\",\n    \"20250507\",\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"预约赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250425\",\n    \"20250426\",\n    \"20250427\",\n    \"20250428\",\n    \"20250506\",\n    \"20250507\",\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"预约赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250425", "20250426", "20250427", "20250428", "20250429", "20250430", "20250501", "20250502", "20250503", "20250504", "20250505", "20250506", "20250507", "20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514"], "渠道二级分类": ["代销 - 中小行"], "产品分类": ["固收+"], "指标名": ["预约赎回金额"]}, "expected_json": {"数据日期": ["20250425", "20250426", "20250427", "20250428", "20250506", "20250507", "20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514"], "渠道二级分类": ["代销 - 中小行"], "产品分类": ["固收+"], "指标名": ["预约赎回金额"]}, "is_consistent": false, "details": "键'数据日期'值不同: 实际=['20250425', '20250426', '20250427', '20250428', '20250429', '20250430', '20250501', '20250502', '20250503', '20250504', '20250505', '20250506', '20250507', '20250508', '20250509', '20250510', '20250511', '20250512', '20250513', '20250514'], 预期=['20250425', '20250426', '20250427', '20250428', '20250506', '20250507', '20250508', '20250509', '20250510', '20250511', '20250512', '20250513', '20250514']", "comparison_result": "不一致"}, {"row_index": 4, "actual_text": "{\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间变化\": [\n    \"是\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间变化\": [\n    \"是\"\n  ]\n}", "actual_json": {"渠道一级分类": ["代销", "零售", "对公"], "指标名": ["持仓规模"], "时间变化": ["是"]}, "expected_json": {"渠道一级分类": ["代销", "零售", "对公"], "指标名": ["持仓规模"], "时间变化": ["是"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 5, "actual_text": "{\n  \"指标名1\": [\n    \"持仓份额\"\n  ],\n  \"指标名2\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收 +\",\n    \"混合\",\n    \"权益\",\n    \"现金类\",\n    \"老理财\"\n  ]\n}", "expected_text": "{\n  \"指标名1\": [\n    \"持仓份额\"\n  ],\n  \"指标名2\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收 +\",\n    \"混合\",\n    \"权益\",\n    \"现金类\",\n    \"老理财\"\n  ]\n}", "actual_json": {"指标名1": ["持仓份额"], "指标名2": ["持仓规模"], "产品分类": ["固定收益类", "固收 +", "混合", "权益", "现金类", "老理财"]}, "expected_json": {"指标名1": ["持仓份额"], "指标名2": ["持仓规模"], "产品分类": ["固定收益类", "固收 +", "混合", "权益", "现金类", "老理财"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 6, "actual_text": "{\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"字段名1\": [\n    \"渠道名称\"\n  ],\n  \"字段名2\": [\n    \"产品分类\"\n  ],\n  \"字段名3\": [\n    \"数据日期\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"字段名1\": [\n    \"渠道名称\"\n  ],\n  \"字段名2\": [\n    \"产品分类\"\n  ],\n  \"字段名3\": [\n    \"数据日期\"\n  ]\n}", "actual_json": {"指标名": ["净申赎金额"], "字段名1": ["渠道名称"], "字段名2": ["产品分类"], "字段名3": ["数据日期"]}, "expected_json": {"指标名": ["净申赎金额"], "字段名1": ["渠道名称"], "字段名2": ["产品分类"], "字段名3": ["数据日期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 7, "actual_text": "{\n  \"产品期限\": [\n    \"人民币理财产品\"\n  ],\n  \"指标名\": [\n    \"产品占比\"\n  ]\n}", "expected_text": "{\n  \"产品期限\": [\n    \"人民币理财产品\"\n  ],\n  \"指标名\": [\n    \"产品占比\"\n  ]\n}", "actual_json": {"产品期限": ["人民币理财产品"], "指标名": ["产品占比"]}, "expected_json": {"产品期限": ["人民币理财产品"], "指标名": ["产品占比"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 8, "actual_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\",\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"至顺1号69期\",\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\",\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"至顺1号69期\",\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20240224"], "渠道一级分类": ["对公", "零售"], "产品简称": ["至顺1号69期", "新启航一年定开24号"], "指标名": ["持仓份额", "持仓规模"]}, "expected_json": {"数据日期": ["20240224"], "渠道一级分类": ["对公", "零售"], "产品简称": ["至顺1号69期", "新启航一年定开24号"], "指标名": ["持仓份额", "持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 9, "actual_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20240224"], "渠道一级分类": ["零售"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20240224"], "渠道一级分类": ["零售"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 10, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R4级 (中高风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R4级 (中高风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20240201"], "渠道一级分类": ["零售"], "风险评级": ["R4级 (中高风险)"], "产品收益类型": ["净值"], "指标名": ["业绩基准", "持仓规模"]}, "expected_json": {"数据日期": ["20240201"], "渠道一级分类": ["零售"], "风险评级": ["R4级 (中高风险)"], "产品收益类型": ["净值"], "指标名": ["业绩基准", "持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 11, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"风险评级\": [\n    \"R3 级 (中等风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"风险评级\": [\n    \"R3 级 (中等风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20240201"], "渠道一级分类": ["零售", "对公"], "风险评级": ["R3 级 (中等风险)"], "币种": ["人民币"], "指标名": ["持仓规模", "持仓客户数"]}, "expected_json": {"数据日期": ["20240201"], "渠道一级分类": ["零售", "对公"], "风险评级": ["R3 级 (中等风险)"], "币种": ["人民币"], "指标名": ["持仓规模", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 12, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品分类\": [\n    \"现金类\"\n  ],\n  \"指标名\": [\n    \"持仓规模总和\"\n  ],\n  \"时间范围\": [\n    \"近20天\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品分类\": [\n    \"现金类\"\n  ],\n  \"指标名\": [\n    \"持仓规模总和\"\n  ],\n  \"时间范围\": [\n    \"近20天\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品分类": ["现金类"], "指标名": ["持仓规模总和"], "时间范围": ["近20天"]}, "expected_json": {"数据日期": ["20250515"], "产品分类": ["现金类"], "指标名": ["持仓规模总和"], "时间范围": ["近20天"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 13, "actual_text": "{\n  \"产品代码\": [\n    \"子产品7天成长4号C\"\n  ],\n  \"组合代码\": [\n    \"7天成长4号\"\n  ],\n  \"指标名\": [\n    \"持仓份额占比\"\n  ]\n}", "expected_text": "{\n  \"产品代码\": [\n    \"子产品7天成长4号C\"\n  ],\n  \"组合代码\": [\n    \"7天成长4号\"\n  ],\n  \"指标名\": [\n    \"持仓份额占比\"\n  ]\n}", "actual_json": {"产品代码": ["子产品7天成长4号C"], "组合代码": ["7天成长4号"], "指标名": ["持仓份额占比"]}, "expected_json": {"产品代码": ["子产品7天成长4号C"], "组合代码": ["7天成长4号"], "指标名": ["持仓份额占比"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 14, "actual_text": "{\n  \"估值方法\": [\n    \"市价法\"\n  ],\n  \"指标名\": [\n    \"产品币种\"\n  ]\n}", "expected_text": "{\n  \"估值方法\": [\n    \"市价法\"\n  ],\n  \"指标名\": [\n    \"产品币种\"\n  ]\n}", "actual_json": {"估值方法": ["市价法"], "指标名": ["产品币种"]}, "expected_json": {"估值方法": ["市价法"], "指标名": ["产品币种"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 15, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售", "对公"], "产品收益类型": ["净值"], "指标名": ["持仓份额", "持仓客户数"]}, "expected_json": {"渠道一级分类": ["零售", "对公"], "产品收益类型": ["净值"], "指标名": ["持仓份额", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 16, "actual_text": "{\n  \"组合代码\": [\n    \"平安理财7天成长4号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"计息基础\"\n  ]\n}", "expected_text": "{\n  \"组合代码\": [\n    \"平安理财7天成长4号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"计息基础\"\n  ]\n}", "actual_json": {"组合代码": ["平安理财7天成长4号"], "产品分类": ["固定收益类"], "币种": ["人民币"], "指标名": ["计息基础"]}, "expected_json": {"组合代码": ["平安理财7天成长4号"], "产品分类": ["固定收益类"], "币种": ["人民币"], "指标名": ["计息基础"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 17, "actual_text": "{\n  \"产品风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"开放类型\": [\n    \"开放式\",\n    \"封闭式\"\n  ],\n  \"指标名\": [\n    \"开放类型含义\",\n    \"下一开放日\"\n  ]\n}", "expected_text": "{\n  \"产品风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"开放类型\": [\n    \"开放式\",\n    \"封闭式\"\n  ],\n  \"指标名\": [\n    \"开放类型含义\",\n    \"下一开放日\"\n  ]\n}", "actual_json": {"产品风险评级": ["R3级 (中等风险)"], "产品收益类型": ["净值"], "开放类型": ["开放式", "封闭式"], "指标名": ["开放类型含义", "下一开放日"]}, "expected_json": {"产品风险评级": ["R3级 (中等风险)"], "产品收益类型": ["净值"], "开放类型": ["开放式", "封闭式"], "指标名": ["开放类型含义", "下一开放日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 18, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道名称\": [\n    \"渠道\"\n  ],\n  \"产品简称\": [\n    \"产品\"\n  ],\n  \"组合名称\": [\n    \"组合\"\n  ],\n  \"产品系列\": [\n    \"产品系列\"\n  ],\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"成立日\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道名称\": [\n    \"渠道\"\n  ],\n  \"产品简称\": [\n    \"产品\"\n  ],\n  \"组合名称\": [\n    \"组合\"\n  ],\n  \"产品系列\": [\n    \"产品系列\"\n  ],\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"成立日\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "渠道名称": ["渠道"], "产品简称": ["产品"], "组合名称": ["组合"], "产品系列": ["产品系列"], "客户类型": ["个人"], "币种": ["人民币"], "指标名": ["持仓客户数", "成立日"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "渠道名称": ["渠道"], "产品简称": ["产品"], "组合名称": ["组合"], "产品系列": ["产品系列"], "客户类型": ["个人"], "币种": ["人民币"], "指标名": ["持仓客户数", "成立日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 19, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"开放类型含义\"\n  ],\n  \"产品系列\": [\n    \"人民币净值型非标产品\"\n  ],\n  \"组合代码\": [\n    \"非优先股\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"开放类型含义\"\n  ],\n  \"产品系列\": [\n    \"人民币净值型非标产品\"\n  ],\n  \"组合代码\": [\n    \"非优先股\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "指标名": ["持仓份额", "开放类型含义"], "产品系列": ["人民币净值型非标产品"], "组合代码": ["非优先股"]}, "expected_json": {"数据日期": ["20250224"], "指标名": ["持仓份额", "开放类型含义"], "产品系列": ["人民币净值型非标产品"], "组合代码": ["非优先股"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 20, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"渠道二级分类\": [\n    \"代销_国股行\",\n    \"代销_中小行\",\n    \"代销_互联网银行\",\n    \"代销_同业机构\"\n  ],\n  \"组合代码\": [\n    \"*\"\n  ],\n  \"产品代码\": [\n    \"*\"\n  ],\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"产品风险评级\": [\n    \"R4级 (中高风险)\"\n  ],\n  \"产品收益类型\": [\n    \"预期收益\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ],\n  \"企划费率期限\": [\n    \"3Y及以上\"\n  ],\n  \"募集开始日\": [\n    \"*\"\n  ],\n  \"募集结束日\": [\n    \"*\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"渠道二级分类\": [\n    \"代销_国股行\",\n    \"代销_中小行\",\n    \"代销_互联网银行\",\n    \"代销_同业机构\"\n  ],\n  \"组合代码\": [\n    \"*\"\n  ],\n  \"产品代码\": [\n    \"*\"\n  ],\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"产品风险评级\": [\n    \"R4级 (中高风险)\"\n  ],\n  \"产品收益类型\": [\n    \"预期收益\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ],\n  \"企划费率期限\": [\n    \"3Y及以上\"\n  ],\n  \"募集开始日\": [\n    \"*\"\n  ],\n  \"募集结束日\": [\n    \"*\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["零售"], "渠道二级分类": ["代销_国股行", "代销_中小行", "代销_互联网银行", "代销_同业机构"], "组合代码": ["*"], "产品代码": ["*"], "客户类型": ["个人"], "产品风险评级": ["R4级 (中高风险)"], "产品收益类型": ["预期收益"], "运作模式": ["封闭式"], "企划费率期限": ["3Y及以上"], "募集开始日": ["*"], "募集结束日": ["*"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["零售"], "渠道二级分类": ["代销_国股行", "代销_中小行", "代销_互联网银行", "代销_同业机构"], "组合代码": ["*"], "产品代码": ["*"], "客户类型": ["个人"], "产品风险评级": ["R4级 (中高风险)"], "产品收益类型": ["预期收益"], "运作模式": ["封闭式"], "企划费率期限": ["3Y及以上"], "募集开始日": ["*"], "募集结束日": ["*"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 21, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品分类\": [\n    \"权益\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"上一开放日\"\n  ],\n  \"业绩基准\": [\n    \"<3%\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品分类\": [\n    \"权益\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"上一开放日\"\n  ],\n  \"业绩基准\": [\n    \"<3%\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "产品分类": ["权益"], "指标名": ["持仓份额", "上一开放日"], "业绩基准": ["<3%"]}, "expected_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "产品分类": ["权益"], "指标名": ["持仓份额", "上一开放日"], "业绩基准": ["<3%"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 22, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250515"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 23, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品简称": ["新启航三个月定开1号"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250515"], "产品简称": ["新启航三个月定开1号"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 24, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250515"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 25, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250515"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 26, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 27, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长汇稳28天持有\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长汇稳28天持有\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品简称": ["平安理财灵活成长汇稳28天持有"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250515"], "产品简称": ["平安理财灵活成长汇稳28天持有"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 28, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 29, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250515"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 30, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250515"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 31, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 32, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 33, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 34, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 35, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 36, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 37, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 38, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 39, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "组合简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "组合简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 40, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航创利\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航创利\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "组合简称": ["启航创利"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "组合简称": ["启航创利"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 41, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 42, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"渠道二级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"渠道二级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "渠道二级分类": ["代销_国股行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "渠道二级分类": ["代销_国股行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 43, "actual_text": "{\n  \"指标名\": [\n    \"持仓占比\"\n  ],\n  \"对比时间\": [\n    \"去年同期\"\n  ],\n  \"维度\": [\n    \"风险等级\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓占比\"\n  ],\n  \"对比时间\": [\n    \"去年同期\"\n  ],\n  \"维度\": [\n    \"风险等级\"\n  ]\n}", "actual_json": {"指标名": ["持仓占比"], "对比时间": ["去年同期"], "维度": ["风险等级"]}, "expected_json": {"指标名": ["持仓占比"], "对比时间": ["去年同期"], "维度": ["风险等级"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 44, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"持仓规模\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售"], "指标名": ["持仓客户数", "持仓规模"]}, "expected_json": {"渠道一级分类": ["零售"], "指标名": ["持仓客户数", "持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 45, "actual_text": "{\n  \"产品分类\": [\n    \"老理财\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"产品分类\": [\n    \"老理财\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"产品分类": ["老理财"], "指标名": ["持仓规模", "持仓客户数"]}, "expected_json": {"产品分类": ["老理财"], "指标名": ["持仓规模", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 46, "actual_text": "{\n  \"产品系列\": [\n    \"灵活成长\"\n  ],\n  \"数据日期\": [\n    \"20250101\"\n  ],\n  \"指标名\": [\n    \"持仓走势\",\n    \"波动最大时期\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"灵活成长\"\n  ],\n  \"数据日期\": [\n    \"20250101\"\n  ],\n  \"指标名\": [\n    \"持仓走势\",\n    \"波动最大时期\"\n  ]\n}", "actual_json": {"产品系列": ["灵活成长"], "数据日期": ["20250101"], "指标名": ["持仓走势", "波动最大时期"]}, "expected_json": {"产品系列": ["灵活成长"], "数据日期": ["20250101"], "指标名": ["持仓走势", "波动最大时期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 47, "actual_text": "{\n  \"产品系列\": [\n    \"卓稳\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"成立以来年化收益率\",\n    \"持仓客户数\",\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"产品系列\",\n    \"产品分类\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"卓稳\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"成立以来年化收益率\",\n    \"持仓客户数\",\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"产品系列\",\n    \"产品分类\"\n  ]\n}", "actual_json": {"产品系列": ["卓稳"], "产品分类": ["固定收益类", "固收+"], "指标名": ["成立以来年化收益率", "持仓客户数", "持仓规模"], "对比维度": ["产品系列", "产品分类"]}, "expected_json": {"产品系列": ["卓稳"], "产品分类": ["固定收益类", "固收+"], "指标名": ["成立以来年化收益率", "持仓客户数", "持仓规模"], "对比维度": ["产品系列", "产品分类"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 48, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\",\n    \"增长率\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"未知\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\",\n    \"增长率\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"未知\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模", "增长率"], "渠道一级分类": ["零售"], "产品简称": ["未知"]}, "expected_json": {"指标名": ["持仓规模", "增长率"], "渠道一级分类": ["零售"], "产品简称": ["未知"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 49, "actual_text": "{\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"排序\": [\n    \"5\"\n  ],\n  \"排序方向\": [\n    \"前\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"排序\": [\n    \"5\"\n  ],\n  \"排序方向\": [\n    \"前\"\n  ]\n}", "actual_json": {"指标名": ["净申赎金额"], "排序": ["5"], "排序方向": ["前"]}, "expected_json": {"指标名": ["净申赎金额"], "排序": ["5"], "排序方向": ["前"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 50, "actual_text": "{\n  \"产品简称\": [\n    \"天天成长C\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"天天成长C\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"产品简称": ["天天成长C"], "指标名": ["净申赎金额"]}, "expected_json": {"产品简称": ["天天成长C"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 51, "actual_text": "{\n  \"产品简称\": [\n    \"天天成长A\"\n  ],\n  \"指标名\": [\n    \"净申赎占比\"\n  ],\n  \"对比时间点\": [\n    \"上月末\",\n    \"季度末\"\n  ],\n  \"变化趋势\": [\n    \"上升\"\n  ],\n  \"维度\": [\n    \"渠道\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"天天成长A\"\n  ],\n  \"指标名\": [\n    \"净申赎占比\"\n  ],\n  \"对比时间点\": [\n    \"上月末\",\n    \"季度末\"\n  ],\n  \"变化趋势\": [\n    \"上升\"\n  ],\n  \"维度\": [\n    \"渠道\"\n  ]\n}", "actual_json": {"产品简称": ["天天成长A"], "指标名": ["净申赎占比"], "对比时间点": ["上月末", "季度末"], "变化趋势": ["上升"], "维度": ["渠道"]}, "expected_json": {"产品简称": ["天天成长A"], "指标名": ["净申赎占比"], "对比时间点": ["上月末", "季度末"], "变化趋势": ["上升"], "维度": ["渠道"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 52, "actual_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"预约申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"预约申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250514"], "产品分类": ["固收+"], "指标名": ["预约申购金额"]}, "expected_json": {"数据日期": ["20250514"], "产品分类": ["固收+"], "指标名": ["预约申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 53, "actual_text": "{\n  \"产品系列\": [\n    \"安心稳健\"\n  ],\n  \"产品大类明细_企划\": [\n    \"日开\"\n  ],\n  \"时间范围\": [\n    \"最近一周\"\n  ],\n  \"排序方式\": [\n    \"时间降序\"\n  ],\n  \"指标名\": [\n    \"申赎情况\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"安心稳健\"\n  ],\n  \"产品大类明细_企划\": [\n    \"日开\"\n  ],\n  \"时间范围\": [\n    \"最近一周\"\n  ],\n  \"排序方式\": [\n    \"时间降序\"\n  ],\n  \"指标名\": [\n    \"申赎情况\"\n  ]\n}", "actual_json": {"产品系列": ["安心稳健"], "产品大类明细_企划": ["日开"], "时间范围": ["最近一周"], "排序方式": ["时间降序"], "指标名": ["申赎情况"]}, "expected_json": {"产品系列": ["安心稳健"], "产品大类明细_企划": ["日开"], "时间范围": ["最近一周"], "排序方式": ["时间降序"], "指标名": ["申赎情况"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 54, "actual_text": "{\n  \"数据日期\": [\n    \"20250516\"\n  ],\n  \"指标名\": [\n    \"预约赎回金额\"\n  ],\n  \"限制条件\": [\n    \"最多10只产品\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250516\"\n  ],\n  \"指标名\": [\n    \"预约赎回金额\"\n  ],\n  \"限制条件\": [\n    \"最多10只产品\"\n  ]\n}", "actual_json": {"数据日期": ["20250516"], "指标名": ["预约赎回金额"], "限制条件": ["最多10只产品"]}, "expected_json": {"数据日期": ["20250516"], "指标名": ["预约赎回金额"], "限制条件": ["最多10只产品"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 55, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 56, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"], "时间范围": ["上个月同期"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"], "时间范围": ["上个月同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 57, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 58, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"], "时间范围": ["上一年同期"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"], "时间范围": ["上一年同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 59, "actual_text": "{\n  \"数据日期\": [\n    \"20250513\",\n    \"20250514\",\n    \"20250515\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250513\",\n    \"20250514\",\n    \"20250515\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250513", "20250514", "20250515"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250513", "20250514", "20250515"], "产品分类": ["固定收益类"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 60, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"], "时间范围": ["上个月同期"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"], "时间范围": ["上个月同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 61, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 62, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"], "时间范围": ["上一年同期"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"], "时间范围": ["上一年同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 63, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 64, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 65, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 66, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 67, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 68, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 69, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 70, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 71, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 72, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 73, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 74, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 75, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 76, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"灵活成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"灵活成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品系列": ["灵活成长"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品系列": ["灵活成长"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 77, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 78, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 79, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "组合简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "组合简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 80, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 81, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上个月同期\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上个月同期\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓规模"], "对比维度": ["上个月同期"], "对比类型": ["变动量"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓规模"], "对比维度": ["上个月同期"], "对比类型": ["变动量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 82, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "expected_json": {"数据日期": ["20250320"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 83, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比类型\": [\n    \"同比\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比类型\": [\n    \"同比\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓规模"], "对比类型": ["同比"], "时间范围": ["上一年同期"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓规模"], "对比类型": ["同比"], "时间范围": ["上一年同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 84, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 85, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "时间范围": ["上个月同期"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "时间范围": ["上个月同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 86, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"同比\"\n  ],\n  \"对比年份\": [\n    \"上一年\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"同比\"\n  ],\n  \"对比年份\": [\n    \"上一年\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "时间范围": ["同比"], "对比年份": ["上一年"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "时间范围": ["同比"], "对比年份": ["上一年"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 87, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 88, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比基准\": [\n    \"本年初\"\n  ],\n  \"变动量\": [\n    \"变动了多少\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比基准\": [\n    \"本年初\"\n  ],\n  \"变动量\": [\n    \"变动了多少\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "对比基准": ["本年初"], "变动量": ["变动了多少"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销_国股行"], "指标名": ["持仓客户数"], "对比基准": ["本年初"], "变动量": ["变动了多少"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 89, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 90, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"], "时间范围": ["上个月同期"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"], "时间范围": ["上个月同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 91, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 92, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"], "时间范围": ["上一年同期"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"], "时间范围": ["上一年同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 93, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 94, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比基准\": [\n    \"本年初\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比基准\": [\n    \"本年初\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"], "对比基准": ["本年初"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"], "对比基准": ["本年初"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 95, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 96, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 97, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 98, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 99, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 100, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 101, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["招商银行"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["招商银行"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 102, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 103, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 104, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道一级分类": ["代销"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道一级分类": ["代销"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 105, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有"], "渠道名称": ["招商银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 106, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "渠道一级分类": ["代销"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 107, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "渠道一级分类": ["代销"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 108, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "时间范围": ["上个月同期"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "时间范围": ["上个月同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 109, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比维度\": [\n    \"上日\"\n  ],\n  \"对比类型\": [\n    \"变动量\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "对比维度": ["上日"], "对比类型": ["变动量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 110, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比类型\": [\n    \"同比\"\n  ],\n  \"对比时间\": [\n    \"20240320\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比类型\": [\n    \"同比\"\n  ],\n  \"对比时间\": [\n    \"20240320\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "对比类型": ["同比"], "对比时间": ["20240320"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "对比类型": ["同比"], "对比时间": ["20240320"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 111, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上个月同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "时间范围": ["上个月同期"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "时间范围": ["上个月同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 112, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比日期\": [\n    \"20250319\"\n  ],\n  \"操作\": [\n    \"对比\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"对比日期\": [\n    \"20250319\"\n  ],\n  \"操作\": [\n    \"对比\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "对比日期": ["20250319"], "操作": ["对比"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "对比日期": ["20250319"], "操作": ["对比"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 113, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"时间范围\": [\n    \"上一年同期\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "时间范围": ["上一年同期"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "时间范围": ["上一年同期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 114, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 115, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 116, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "渠道名称": ["招商银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 117, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 118, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 119, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"启航\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"启航\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品系列": ["启航"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品系列": ["启航"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 120, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "客户类型": ["机构"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "客户类型": ["机构"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 121, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 122, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 123, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"组合简称\": [\n    \"启航\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "组合简称": ["启航"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "组合简称": ["启航"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 124, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 125, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有A\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有A\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有A"], "客户类型": ["机构"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有A"], "客户类型": ["机构"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 126, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有A\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有A\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有A"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有A"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 127, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有A\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"灵活成长汇稳28天持有A\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有A"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["灵活成长汇稳28天持有A"], "客户类型": ["机构"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 128, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "客户类型": ["机构"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 129, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 130, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 131, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 132, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 133, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 134, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "风险评级": ["R1 级 (低等风险)"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 135, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 136, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 137, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "风险评级": ["R1级 (低等风险)"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 138, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 139, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 140, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 141, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 142, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 143, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品开放频率": ["每日开放"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品开放频率": ["每日开放"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 144, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 145, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品开放频率": ["每日开放"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 146, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品开放频率\": [\n    \"每日开放\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品开放频率": ["每日开放"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 147, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 148, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 149, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 150, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 151, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 152, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "募集方式": ["公募"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "募集方式": ["公募"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 153, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 154, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "募集方式": ["公募"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 155, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "募集方式": ["公募"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 156, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "开放类型": ["开放式"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "开放类型": ["开放式"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 157, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "开放类型": ["开放式"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "开放类型": ["开放式"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 158, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "开放类型": ["开放式"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "开放类型": ["开放式"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 159, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "开放类型": ["开放式"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "开放类型": ["开放式"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 160, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "开放类型": ["开放式"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓规模"], "开放类型": ["开放式"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 161, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "开放类型": ["开放式"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "开放类型": ["开放式"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 162, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "开放类型": ["开放式"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "开放类型": ["开放式"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 163, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "开放类型": ["开放式"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "开放类型": ["开放式"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 164, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "开放类型": ["开放式"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["持仓客户数"], "开放类型": ["开放式"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 165, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 166, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 167, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 168, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 169, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 170, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 171, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 172, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航增强\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航增强"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 173, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 174, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 175, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 176, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 177, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 178, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 179, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 180, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号5期\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["天天成长3号5期"], "产品期限": ["3M"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 181, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 182, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品期限\": [\n    \"3M\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250320"], "产品期限": ["3M"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 183, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "指标名": ["持仓规模", "持仓份额"]}, "expected_json": {"数据日期": ["20250515"], "指标名": ["持仓规模", "持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 184, "actual_text": "{\n  \"数据日期\": [\n    \"20250509\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250509\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250509"], "渠道一级分类": ["代销"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250509"], "渠道一级分类": ["代销"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 185, "actual_text": "{\n  \"数据日期\": [\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "expected_json": {"数据日期": ["20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514"], "渠道名称": ["国有股份制银行"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 186, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"互联网银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"字段名3\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"互联网银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"字段名3\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道名称": ["互联网银行"], "指标名": ["持仓规模"], "字段名3": ["产品系列"]}, "expected_json": {"数据日期": ["20250515"], "渠道名称": ["互联网银行"], "指标名": ["持仓规模"], "字段名3": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 187, "actual_text": "{\n  \"数据日期\": [\n    \"20250401\",\n    \"20250501\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250401\",\n    \"20250501\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250401", "20250501"], "渠道一级分类": ["对公"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250401", "20250501"], "渠道一级分类": ["对公"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 188, "actual_text": "{\n  \"数据日期\": [\n    \"20250508\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号47期A\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250508\"\n  ],\n  \"产品简称\": [\n    \"天天成长3号47期A\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250508"], "产品简称": ["天天成长3号47期A"], "指标名": ["持仓份额", "持仓规模"]}, "expected_json": {"数据日期": ["20250508"], "产品简称": ["天天成长3号47期A"], "指标名": ["持仓份额", "持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 189, "actual_text": "{\n  \"数据日期\": [\n    \"20250505\",\n    \"20250506\",\n    \"20250507\",\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模总和\",\n    \"平均持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250505\",\n    \"20250506\",\n    \"20250507\",\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模总和\",\n    \"平均持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250505", "20250506", "20250507", "20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514"], "产品系列": ["天天成长"], "指标名": ["持仓规模总和", "平均持仓份额"]}, "expected_json": {"数据日期": ["20250505", "20250506", "20250507", "20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514"], "产品系列": ["天天成长"], "指标名": ["持仓规模总和", "平均持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 190, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品分类\": [\n    \"混合\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\",\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品分类\": [\n    \"混合\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\",\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道一级分类": ["零售"], "产品分类": ["混合"], "指标名": ["净申赎金额", "持仓规模"]}, "expected_json": {"数据日期": ["20250515"], "渠道一级分类": ["零售"], "产品分类": ["混合"], "指标名": ["净申赎金额", "持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 191, "actual_text": "{\n  \"时间范围\": [\n    \"近15天\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"产品期限\": [\n    \"90天持有\"\n  ],\n  \"指标名\": [\n    \"净申赎金额总和\",\n    \"净申赎金额为正的天数占比\"\n  ]\n}", "expected_text": "{\n  \"时间范围\": [\n    \"近15天\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"产品期限\": [\n    \"90天持有\"\n  ],\n  \"指标名\": [\n    \"净申赎金额总和\",\n    \"净申赎金额为正的天数占比\"\n  ]\n}", "actual_json": {"时间范围": ["近15天"], "产品分类": ["固收+"], "产品期限": ["90天持有"], "指标名": ["净申赎金额总和", "净申赎金额为正的天数占比"]}, "expected_json": {"时间范围": ["近15天"], "产品分类": ["固收+"], "产品期限": ["90天持有"], "指标名": ["净申赎金额总和", "净申赎金额为正的天数占比"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 192, "actual_text": "{\n  \"数据日期\": [\n    \"20250501\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\"\n  ],\n  \"指标名\": [\n    \"预约申购金额平均值\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收 +\",\n    \"混合\",\n    \"权益\",\n    \"现金类\",\n    \"老理财\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250501\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\"\n  ],\n  \"指标名\": [\n    \"预约申购金额平均值\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收 +\",\n    \"混合\",\n    \"权益\",\n    \"现金类\",\n    \"老理财\"\n  ]\n}", "actual_json": {"数据日期": ["20250501"], "渠道二级分类": ["代销 - 中小行"], "指标名": ["预约申购金额平均值"], "产品分类": ["固定收益类", "固收 +", "混合", "权益", "现金类", "老理财"]}, "expected_json": {"数据日期": ["20250501"], "渠道二级分类": ["代销 - 中小行"], "指标名": ["预约申购金额平均值"], "产品分类": ["固定收益类", "固收 +", "混合", "权益", "现金类", "老理财"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 193, "actual_text": "{\n  \"数据日期\": [\n    \"20250426\",\n    \"20250427\",\n    \"20250428\",\n    \"20250429\",\n    \"20250430\",\n    \"20250501\",\n    \"20250502\",\n    \"20250503\",\n    \"20250504\",\n    \"20250505\",\n    \"20250506\",\n    \"20250507\",\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\",\n    \"20250515\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"预约赎回金额\"\n  ],\n  \"聚合操作\": [\n    \"汇总\"\n  ],\n  \"统计指标\": [\n    \"涉及的产品数量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250426\",\n    \"20250427\",\n    \"20250428\",\n    \"20250429\",\n    \"20250430\",\n    \"20250501\",\n    \"20250502\",\n    \"20250503\",\n    \"20250504\",\n    \"20250505\",\n    \"20250506\",\n    \"20250507\",\n    \"20250508\",\n    \"20250509\",\n    \"20250510\",\n    \"20250511\",\n    \"20250512\",\n    \"20250513\",\n    \"20250514\",\n    \"20250515\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"预约赎回金额\"\n  ],\n  \"聚合操作\": [\n    \"汇总\"\n  ],\n  \"统计指标\": [\n    \"涉及的产品数量\"\n  ]\n}", "actual_json": {"数据日期": ["20250426", "20250427", "20250428", "20250429", "20250430", "20250501", "20250502", "20250503", "20250504", "20250505", "20250506", "20250507", "20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514", "20250515"], "渠道二级分类": ["代销 - 中小行"], "产品分类": ["固收+"], "指标名": ["预约赎回金额"], "聚合操作": ["汇总"], "统计指标": ["涉及的产品数量"]}, "expected_json": {"数据日期": ["20250426", "20250427", "20250428", "20250429", "20250430", "20250501", "20250502", "20250503", "20250504", "20250505", "20250506", "20250507", "20250508", "20250509", "20250510", "20250511", "20250512", "20250513", "20250514", "20250515"], "渠道二级分类": ["代销 - 中小行"], "产品分类": ["固收+"], "指标名": ["预约赎回金额"], "聚合操作": ["汇总"], "统计指标": ["涉及的产品数量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 194, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"90天成长\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"持仓规模\"\n  ],\n  \"运算符\": [\n    \"总和\",\n    \"平均\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"90天成长\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"持仓规模\"\n  ],\n  \"运算符\": [\n    \"总和\",\n    \"平均\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道一级分类": ["零售"], "产品简称": ["90天成长"], "产品收益类型": ["净值"], "指标名": ["持仓客户数", "持仓规模"], "运算符": ["总和", "平均"]}, "expected_json": {"数据日期": ["20250515"], "渠道一级分类": ["零售"], "产品简称": ["90天成长"], "产品收益类型": ["净值"], "指标名": ["持仓客户数", "持仓规模"], "运算符": ["总和", "平均"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 195, "actual_text": "{\n  \"数据日期\": [\n    \"20250501\",\n    \"20250515\"\n  ],\n  \"产品系列\": [\n    \"启航增强\"\n  ],\n  \"持仓客户数\": [\n    \"100000\"\n  ],\n  \"指标名\": [\n    \"渠道名称\",\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250501\",\n    \"20250515\"\n  ],\n  \"产品系列\": [\n    \"启航增强\"\n  ],\n  \"持仓客户数\": [\n    \"100000\"\n  ],\n  \"指标名\": [\n    \"渠道名称\",\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250501", "20250515"], "产品系列": ["启航增强"], "持仓客户数": ["100000"], "指标名": ["渠道名称", "持仓规模"]}, "expected_json": {"数据日期": ["20250501", "20250515"], "产品系列": ["启航增强"], "持仓客户数": ["100000"], "指标名": ["渠道名称", "持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 196, "actual_text": "{\n  \"数据日期\": [\n    \"20250508\",\n    \"20250515\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓份额总和\",\n    \"持仓份额增减比例\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250508\",\n    \"20250515\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓份额总和\",\n    \"持仓份额增减比例\"\n  ]\n}", "actual_json": {"数据日期": ["20250508", "20250515"], "渠道一级分类": ["对公"], "指标名": ["持仓份额总和", "持仓份额增减比例"]}, "expected_json": {"数据日期": ["20250508", "20250515"], "渠道一级分类": ["对公"], "指标名": ["持仓份额总和", "持仓份额增减比例"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 197, "actual_text": "{\n  \"数据日期\": [\n    \"20250401\"\n  ],\n  \"指标名\": [\n    \"持仓规模比例\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ],\n  \"操作\": [\n    \"分组\",\n    \"计算\",\n    \"找出\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250401\"\n  ],\n  \"指标名\": [\n    \"持仓规模比例\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ],\n  \"操作\": [\n    \"分组\",\n    \"计算\",\n    \"找出\"\n  ]\n}", "actual_json": {"数据日期": ["20250401"], "指标名": ["持仓规模比例"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"], "操作": ["分组", "计算", "找出"]}, "expected_json": {"数据日期": ["20250401"], "指标名": ["持仓规模比例"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"], "操作": ["分组", "计算", "找出"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 198, "actual_text": "{\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"时间范围\": [\n    \"近30天\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"时间范围\": [\n    \"近30天\"\n  ]\n}", "actual_json": {"渠道一级分类": ["代销", "零售", "对公"], "指标名": ["净申赎金额"], "时间范围": ["近30天"]}, "expected_json": {"渠道一级分类": ["代销", "零售", "对公"], "指标名": ["净申赎金额"], "时间范围": ["近30天"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 199, "actual_text": "{\n  \"数据日期\": [\n    \"20250507\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收+\",\n    \"混合\",\n    \"权益\",\n    \"现金类\",\n    \"老理财\"\n  ],\n  \"指标名\": [\n    \"预约申购金额中位数\"\n  ],\n  \"时间范围\": [\n    \"过去8天\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250507\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\",\n    \"固收+\",\n    \"混合\",\n    \"权益\",\n    \"现金类\",\n    \"老理财\"\n  ],\n  \"指标名\": [\n    \"预约申购金额中位数\"\n  ],\n  \"时间范围\": [\n    \"过去8天\"\n  ]\n}", "actual_json": {"数据日期": ["20250507"], "产品分类": ["固定收益类", "固收+", "混合", "权益", "现金类", "老理财"], "指标名": ["预约申购金额中位数"], "时间范围": ["过去8天"]}, "expected_json": {"数据日期": ["20250507"], "产品分类": ["固定收益类", "固收+", "混合", "权益", "现金类", "老理财"], "指标名": ["预约申购金额中位数"], "时间范围": ["过去8天"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 200, "actual_text": "{\n  \"指标名\": [\n    \"预约赎回金额\"\n  ],\n  \"对比维度\": [\n    \"产品\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"预约赎回金额\"\n  ],\n  \"对比维度\": [\n    \"产品\"\n  ]\n}", "actual_json": {"指标名": ["预约赎回金额"], "对比维度": ["产品"]}, "expected_json": {"指标名": ["预约赎回金额"], "对比维度": ["产品"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 201, "actual_text": "{\n  \"字段名\": [\n    \"业务日期\"\n  ],\n  \"指标名\": [\n    \"日期跨度\",\n    \"日期缺失\"\n  ]\n}", "expected_text": "{\n  \"字段名\": [\n    \"业务日期\"\n  ],\n  \"指标名\": [\n    \"日期跨度\",\n    \"日期缺失\"\n  ]\n}", "actual_json": {"字段名": ["业务日期"], "指标名": ["日期跨度", "日期缺失"]}, "expected_json": {"字段名": ["业务日期"], "指标名": ["日期跨度", "日期缺失"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 202, "actual_text": "{\n  \"数据日期\": [\n    \"20250401\",\n    \"20250501\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250401\",\n    \"20250501\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"数据日期": ["20250401", "20250501"], "指标名": ["净申赎金额"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"数据日期": ["20250401", "20250501"], "指标名": ["净申赎金额"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 203, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"预约申购金额占比\"\n  ],\n  \"渠道二级分类\": [\n    \"*\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"预约申购金额占比\"\n  ],\n  \"渠道二级分类\": [\n    \"*\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "指标名": ["预约申购金额占比"], "渠道二级分类": ["*"]}, "expected_json": {"数据日期": ["20250515"], "指标名": ["预约申购金额占比"], "渠道二级分类": ["*"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 204, "actual_text": "{\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"指标名": ["持仓客户数"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"指标名": ["持仓客户数"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 205, "actual_text": "{\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"操作\": [\n    \"平均\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"操作\": [\n    \"平均\"\n  ]\n}", "actual_json": {"指标名": ["持仓份额"], "操作": ["平均"]}, "expected_json": {"指标名": ["持仓份额"], "操作": ["平均"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 206, "actual_text": "提取失败", "expected_text": "提取失败", "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 207, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\",\n    \"净申赎金额\"\n  ],\n  \"维度名\": [\n    \"渠道\",\n    \"产品\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\",\n    \"净申赎金额\"\n  ],\n  \"维度名\": [\n    \"渠道\",\n    \"产品\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模", "持仓客户数", "净申赎金额"], "维度名": ["渠道", "产品"]}, "expected_json": {"指标名": ["持仓规模", "持仓客户数", "净申赎金额"], "维度名": ["渠道", "产品"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 208, "actual_text": "{\n  \"数据日期\": [\n    \"20240515\"\n  ],\n  \"时间范围\": [\n    \"近1年\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"趋势\": [\n    \"增长\",\n    \"稳定\",\n    \"下降\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240515\"\n  ],\n  \"时间范围\": [\n    \"近1年\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"趋势\": [\n    \"增长\",\n    \"稳定\",\n    \"下降\"\n  ]\n}", "actual_json": {"数据日期": ["20240515"], "时间范围": ["近1年"], "指标名": ["持仓规模"], "趋势": ["增长", "稳定", "下降"]}, "expected_json": {"数据日期": ["20240515"], "时间范围": ["近1年"], "指标名": ["持仓规模"], "趋势": ["增长", "稳定", "下降"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 209, "actual_text": "{\n  \"指标名1\": [\n    \"持仓客户数\"\n  ],\n  \"指标名2\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ]\n}", "expected_text": "{\n  \"指标名1\": [\n    \"持仓客户数\"\n  ],\n  \"指标名2\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ]\n}", "actual_json": {"指标名1": ["持仓客户数"], "指标名2": ["持仓规模"], "渠道一级分类": ["零售"]}, "expected_json": {"指标名1": ["持仓客户数"], "指标名2": ["持仓规模"], "渠道一级分类": ["零售"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 210, "actual_text": "{\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"产品分类\": [\n    \"不同产品类别\"\n  ],\n  \"数据日期\": [\n    \"不同业务日期\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"产品分类\": [\n    \"不同产品类别\"\n  ],\n  \"数据日期\": [\n    \"不同业务日期\"\n  ]\n}", "actual_json": {"指标名": ["持仓客户数"], "产品分类": ["不同产品类别"], "数据日期": ["不同业务日期"]}, "expected_json": {"指标名": ["持仓客户数"], "产品分类": ["不同产品类别"], "数据日期": ["不同业务日期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 211, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度1\": [\n    \"组合\"\n  ],\n  \"维度2\": [\n    \"渠道\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度1\": [\n    \"组合\"\n  ],\n  \"维度2\": [\n    \"渠道\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "维度1": ["组合"], "维度2": ["渠道"]}, "expected_json": {"指标名": ["持仓规模"], "维度1": ["组合"], "维度2": ["渠道"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 212, "actual_text": "{\n  \"产品系列\": [\n    \"不同系列\"\n  ],\n  \"指标名\": [\n    \"预约申购金额\",\n    \"预约赎回金额\"\n  ],\n  \"问题类型\": [\n    \"比例关系\"\n  ],\n  \"产品分类\": [\n    \"是否因产品大类而异\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"不同系列\"\n  ],\n  \"指标名\": [\n    \"预约申购金额\",\n    \"预约赎回金额\"\n  ],\n  \"问题类型\": [\n    \"比例关系\"\n  ],\n  \"产品分类\": [\n    \"是否因产品大类而异\"\n  ]\n}", "actual_json": {"产品系列": ["不同系列"], "指标名": ["预约申购金额", "预约赎回金额"], "问题类型": ["比例关系"], "产品分类": ["是否因产品大类而异"]}, "expected_json": {"产品系列": ["不同系列"], "指标名": ["预约申购金额", "预约赎回金额"], "问题类型": ["比例关系"], "产品分类": ["是否因产品大类而异"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 213, "actual_text": "{\n  \"组合简称\": [\n    \"母产品\"\n  ],\n  \"指标名1\": [\n    \"产品数量\"\n  ],\n  \"指标名2\": [\n    \"持仓客户数\"\n  ],\n  \"关系\": [\n    \"是否存在关联\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"母产品\"\n  ],\n  \"指标名1\": [\n    \"产品数量\"\n  ],\n  \"指标名2\": [\n    \"持仓客户数\"\n  ],\n  \"关系\": [\n    \"是否存在关联\"\n  ]\n}", "actual_json": {"组合简称": ["母产品"], "指标名1": ["产品数量"], "指标名2": ["持仓客户数"], "关系": ["是否存在关联"]}, "expected_json": {"组合简称": ["母产品"], "指标名1": ["产品数量"], "指标名2": ["持仓客户数"], "关系": ["是否存在关联"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 214, "actual_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"附加条件\": [\n    \"已到期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"附加条件\": [\n    \"已到期\"\n  ]\n}", "actual_json": {"数据日期": ["20240224"], "渠道一级分类": ["零售"], "指标名": ["持仓规模"], "附加条件": ["已到期"]}, "expected_json": {"数据日期": ["20240224"], "渠道一级分类": ["零售"], "指标名": ["持仓规模"], "附加条件": ["已到期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 215, "actual_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20240224"], "指标名": ["持仓规模"]}, "expected_json": {"数据日期": ["20240224"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 216, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"产品数量\"\n  ],\n  \"产品收益类型\": [\n    \"净值\",\n    \"货币\",\n    \"预期收益\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"产品数量\"\n  ],\n  \"产品收益类型\": [\n    \"净值\",\n    \"货币\",\n    \"预期收益\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售"], "指标名": ["产品数量"], "产品收益类型": ["净值", "货币", "预期收益"]}, "expected_json": {"渠道一级分类": ["零售"], "指标名": ["产品数量"], "产品收益类型": ["净值", "货币", "预期收益"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 217, "actual_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"是否含非标资产\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"是否含非标资产\"\n  ]\n}", "actual_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["是否含非标资产"]}, "expected_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["是否含非标资产"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 218, "actual_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"风险评级\"\n  ],\n  \"字段名\": [\n    \"风险评级含义\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"风险评级\"\n  ],\n  \"字段名\": [\n    \"风险评级含义\"\n  ]\n}", "actual_json": {"产品简称": ["天天成长B款"], "指标名": ["风险评级"], "字段名": ["风险评级含义"]}, "expected_json": {"产品简称": ["天天成长B款"], "指标名": ["风险评级"], "字段名": ["风险评级含义"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 219, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240203\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"字段名3\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240203\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"字段名3\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"数据日期": ["20240201", "20240203"], "指标名": ["持仓客户数"], "字段名3": ["产品系列"]}, "expected_json": {"数据日期": ["20240201", "20240203"], "指标名": ["持仓客户数"], "字段名3": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 220, "actual_text": "{\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"日期\",\n    \"渠道代码\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"日期\",\n    \"渠道代码\"\n  ]\n}", "actual_json": {"产品简称": ["7天成长4号C"], "指标名": ["持仓规模"], "维度": ["日期", "渠道代码"]}, "expected_json": {"产品简称": ["7天成长4号C"], "指标名": ["持仓规模"], "维度": ["日期", "渠道代码"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 221, "actual_text": "{\n  \"指标名\": [\n    \"信用等级提升情况\"\n  ],\n  \"渠道名称\": [\n    \"*\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"信用等级提升情况\"\n  ],\n  \"渠道名称\": [\n    \"*\"\n  ]\n}", "actual_json": {"指标名": ["信用等级提升情况"], "渠道名称": ["*"]}, "expected_json": {"指标名": ["信用等级提升情况"], "渠道名称": ["*"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 222, "actual_text": "{\n  \"组合简称\": [\n    \"至顺5号\"\n  ],\n  \"产品风险评级\": [\n    \"R2级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"至顺5号\"\n  ],\n  \"产品风险评级\": [\n    \"R2级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"组合简称": ["至顺5号"], "产品风险评级": ["R2级 (中低风险)"], "产品收益类型": ["货币"], "指标名": ["持仓规模", "持仓客户数"]}, "expected_json": {"组合简称": ["至顺5号"], "产品风险评级": ["R2级 (中低风险)"], "产品收益类型": ["货币"], "指标名": ["持仓规模", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 223, "actual_text": "{\n  \"指标名\": [\n    \"产品期限 - 细口径\"\n  ],\n  \"募集方式\": [\n    \"公募\",\n    \"私募\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"产品期限 - 细口径\"\n  ],\n  \"募集方式\": [\n    \"公募\",\n    \"私募\"\n  ]\n}", "actual_json": {"指标名": ["产品期限 - 细口径"], "募集方式": ["公募", "私募"]}, "expected_json": {"指标名": ["产品期限 - 细口径"], "募集方式": ["公募", "私募"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 224, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240202\",\n    \"20240203\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240202\",\n    \"20240203\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20240201", "20240202", "20240203"], "风险评级": ["R2 级 (中低风险)"], "币种": ["人民币"], "指标名": ["持仓规模", "持仓客户数"]}, "expected_json": {"数据日期": ["20240201", "20240202", "20240203"], "风险评级": ["R2 级 (中低风险)"], "币种": ["人民币"], "指标名": ["持仓规模", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 225, "actual_text": "{\n  \"指标名\": [\n    \"运作模式\"\n  ],\n  \"问题类型\": [\n    \"一致性\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"运作模式\"\n  ],\n  \"问题类型\": [\n    \"一致性\"\n  ]\n}", "actual_json": {"指标名": ["运作模式"], "问题类型": ["一致性"]}, "expected_json": {"指标名": ["运作模式"], "问题类型": ["一致性"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 226, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"成立日\"\n  ],\n  \"维度\": [\n    \"渠道\",\n    \"产品\",\n    \"组合\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\",\n    \"成立日\"\n  ],\n  \"维度\": [\n    \"渠道\",\n    \"产品\",\n    \"组合\"\n  ]\n}", "actual_json": {"数据日期": ["20240201"], "客户类型": ["机构"], "币种": ["人民币"], "指标名": ["持仓客户数", "成立日"], "维度": ["渠道", "产品", "组合"]}, "expected_json": {"数据日期": ["20240201"], "客户类型": ["机构"], "币种": ["人民币"], "指标名": ["持仓客户数", "成立日"], "维度": ["渠道", "产品", "组合"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 227, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"客户类型\": [\n    \"非金融性公司\"\n  ],\n  \"产品增信标识\": [\n    \"有\"\n  ],\n  \"产品币种\": [\n    \"人民币\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"客户类型\": [\n    \"非金融性公司\"\n  ],\n  \"产品增信标识\": [\n    \"有\"\n  ],\n  \"产品币种\": [\n    \"人民币\"\n  ]\n}", "actual_json": {"数据日期": ["20240201"], "指标名": ["持仓规模"], "客户类型": ["非金融性公司"], "产品增信标识": ["有"], "产品币种": ["人民币"]}, "expected_json": {"数据日期": ["20240201"], "指标名": ["持仓规模"], "客户类型": ["非金融性公司"], "产品增信标识": ["有"], "产品币种": ["人民币"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 228, "actual_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20240224"], "渠道一级分类": ["零售"], "产品风险评级": ["R2 级 (中低风险)"], "产品收益类型": ["净值"], "募集方式": ["公募"], "指标名": ["业绩基准", "持仓规模"]}, "expected_json": {"数据日期": ["20240224"], "渠道一级分类": ["零售"], "产品风险评级": ["R2 级 (中低风险)"], "产品收益类型": ["净值"], "募集方式": ["公募"], "指标名": ["业绩基准", "持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 229, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"运作模式\": [\n    \"日开型\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"成立日\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"风险评级\": [\n    \"R1 级 (低等风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"募集方式\": [\n    \"公募\"\n  ],\n  \"运作模式\": [\n    \"日开型\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"成立日\"\n  ]\n}", "actual_json": {"数据日期": ["20240201"], "风险评级": ["R1 级 (低等风险)"], "币种": ["人民币"], "产品收益类型": ["货币"], "募集方式": ["公募"], "运作模式": ["日开型"], "渠道一级分类": ["对公"], "指标名": ["持仓规模", "成立日"]}, "expected_json": {"数据日期": ["20240201"], "风险评级": ["R1 级 (低等风险)"], "币种": ["人民币"], "产品收益类型": ["货币"], "募集方式": ["公募"], "运作模式": ["日开型"], "渠道一级分类": ["对公"], "指标名": ["持仓规模", "成立日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 230, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240225\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240225\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20240201", "20240225"], "产品系列": ["天天成长"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20240201", "20240225"], "产品系列": ["天天成长"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 231, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"聚合方式\": [\n    \"总和\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"聚合方式\": [\n    \"总和\"\n  ]\n}", "actual_json": {"数据日期": ["20240201"], "指标名": ["持仓规模"], "聚合方式": ["总和"]}, "expected_json": {"数据日期": ["20240201"], "指标名": ["持仓规模"], "聚合方式": ["总和"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 232, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240210\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓规模增长率\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240210\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓规模增长率\"\n  ]\n}", "actual_json": {"数据日期": ["20240201", "20240210"], "渠道一级分类": ["对公"], "指标名": ["持仓规模增长率"]}, "expected_json": {"数据日期": ["20240201", "20240210"], "渠道一级分类": ["对公"], "指标名": ["持仓规模增长率"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 233, "actual_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"业务日期\": [\n    \"所有\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"业务日期\": [\n    \"所有\"\n  ]\n}", "actual_json": {"产品简称": ["天天成长B款"], "指标名": ["持仓规模"], "业务日期": ["所有"]}, "expected_json": {"产品简称": ["天天成长B款"], "指标名": ["持仓规模"], "业务日期": ["所有"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 234, "actual_text": "{\n  \"组合简称\": [\n    \"7天成长4号\"\n  ],\n  \"数据日期\": [\n    \"20241101\",\n    \"20241110\"\n  ],\n  \"指标名\": [\n    \"持仓规模的移动平均值\"\n  ],\n  \"窗口大小\": [\n    \"3\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"7天成长4号\"\n  ],\n  \"数据日期\": [\n    \"20241101\",\n    \"20241110\"\n  ],\n  \"指标名\": [\n    \"持仓规模的移动平均值\"\n  ],\n  \"窗口大小\": [\n    \"3\"\n  ]\n}", "actual_json": {"组合简称": ["7天成长4号"], "数据日期": ["20241101", "20241110"], "指标名": ["持仓规模的移动平均值"], "窗口大小": ["3"]}, "expected_json": {"组合简称": ["7天成长4号"], "数据日期": ["20241101", "20241110"], "指标名": ["持仓规模的移动平均值"], "窗口大小": ["3"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 235, "actual_text": "{\n  \"产品简称\": [\n    \"天天成长3号1期\",\n    \"7天成长4号\",\n    \"至顺5号95期\"\n  ],\n  \"产品分类\": [\n    \"已到期\",\n    \"人民币产品\"\n  ],\n  \"产品系列\": [\n    \"不同产品系列\"\n  ],\n  \"组合代码\": [\n    \"不同组合\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"天天成长3号1期\",\n    \"7天成长4号\",\n    \"至顺5号95期\"\n  ],\n  \"产品分类\": [\n    \"已到期\",\n    \"人民币产品\"\n  ],\n  \"产品系列\": [\n    \"不同产品系列\"\n  ],\n  \"组合代码\": [\n    \"不同组合\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"产品简称": ["天天成长3号1期", "7天成长4号", "至顺5号95期"], "产品分类": ["已到期", "人民币产品"], "产品系列": ["不同产品系列"], "组合代码": ["不同组合"], "指标名": ["持仓份额", "持仓客户数"]}, "expected_json": {"产品简称": ["天天成长3号1期", "7天成长4号", "至顺5号95期"], "产品分类": ["已到期", "人民币产品"], "产品系列": ["不同产品系列"], "组合代码": ["不同组合"], "指标名": ["持仓份额", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 236, "actual_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"产品简称": ["新启航一年定开24号"], "渠道一级分类": ["零售"], "产品收益类型": ["净值"], "指标名": ["持仓客户数"]}, "expected_json": {"产品简称": ["新启航一年定开24号"], "渠道一级分类": ["零售"], "产品收益类型": ["净值"], "指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 237, "actual_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240225\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20240201\",\n    \"20240225\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"数据日期": ["20240201", "20240225"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"数据日期": ["20240201", "20240225"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 238, "actual_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模比例\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模比例\"\n  ]\n}", "actual_json": {"数据日期": ["20250514"], "产品简称": ["天天成长B款"], "渠道一级分类": ["零售"], "指标名": ["持仓规模比例"]}, "expected_json": {"数据日期": ["20250514"], "产品简称": ["天天成长B款"], "渠道一级分类": ["零售"], "指标名": ["持仓规模比例"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 239, "actual_text": "{\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"数据日期\": [\n    \"20240201\",\n    \"20240210\"\n  ],\n  \"指标名\": [\n    \"持仓规模累计增长率\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"数据日期\": [\n    \"20240201\",\n    \"20240210\"\n  ],\n  \"指标名\": [\n    \"持仓规模累计增长率\"\n  ]\n}", "actual_json": {"组合简称": ["天天成长B款"], "数据日期": ["20240201", "20240210"], "指标名": ["持仓规模累计增长率"]}, "expected_json": {"组合简称": ["天天成长B款"], "数据日期": ["20240201", "20240210"], "指标名": ["持仓规模累计增长率"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 240, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250224\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"维度\": [\n    \"产品系列\"\n  ],\n  \"趋势\": [\n    \"变化趋势\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250224\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"维度\": [\n    \"产品系列\"\n  ],\n  \"趋势\": [\n    \"变化趋势\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250224"], "指标名": ["持仓客户数"], "维度": ["产品系列"], "趋势": ["变化趋势"]}, "expected_json": {"数据日期": ["20250201", "20250224"], "指标名": ["持仓客户数"], "维度": ["产品系列"], "趋势": ["变化趋势"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 241, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"风险评级\": [\n    \"R3级\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"风险评级\": [\n    \"R3级\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "风险评级": ["R3级"], "指标名": ["业绩基准", "计息基础"]}, "expected_json": {"数据日期": ["20250224"], "风险评级": ["R3级"], "指标名": ["业绩基准", "计息基础"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 242, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"日均持仓规模\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"日均持仓规模\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["对公"], "产品收益类型": ["净值"], "产品简称": ["天天成长B款"], "指标名": ["日均持仓规模"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["对公"], "产品收益类型": ["净值"], "产品简称": ["天天成长B款"], "指标名": ["日均持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 243, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"产品简称\": [\n    \"启航成长一年定开40号\"\n  ],\n  \"指标名\": [\n    \"上一开放日\",\n    \"下一开放日\"\n  ],\n  \"开放类型\": [\n    \"定开型\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"产品简称\": [\n    \"启航成长一年定开40号\"\n  ],\n  \"指标名\": [\n    \"上一开放日\",\n    \"下一开放日\"\n  ],\n  \"开放类型\": [\n    \"定开型\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "产品简称": ["启航成长一年定开40号"], "指标名": ["上一开放日", "下一开放日"], "开放类型": ["定开型"]}, "expected_json": {"数据日期": ["20250224"], "产品简称": ["启航成长一年定开40号"], "指标名": ["上一开放日", "下一开放日"], "开放类型": ["定开型"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 244, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"募集方式\": [\n    \"私募\"\n  ],\n  \"指标名\": [\n    \"产品数量\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"募集方式\": [\n    \"私募\"\n  ],\n  \"指标名\": [\n    \"产品数量\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售"], "产品收益类型": ["净值"], "募集方式": ["私募"], "指标名": ["产品数量"]}, "expected_json": {"渠道一级分类": ["零售"], "产品收益类型": ["净值"], "募集方式": ["私募"], "指标名": ["产品数量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 245, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"估值方法\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"估值方法\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "指标名": ["业绩基准", "估值方法"]}, "expected_json": {"渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "指标名": ["业绩基准", "估值方法"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 246, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓客户数\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"持仓客户数\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售", "对公"], "指标名": ["持仓份额", "持仓客户数"], "产品收益类型": ["净值"], "币种": ["人民币"]}, "expected_json": {"渠道一级分类": ["零售", "对公"], "指标名": ["持仓份额", "持仓客户数"], "产品收益类型": ["净值"], "币种": ["人民币"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 247, "actual_text": "{\n  \"渠道二级分类\": [\n    \"同业机构\"\n  ],\n  \"产品系列\": [\n    \"组合下\"\n  ],\n  \"产品代码\": [\n    \"组合下\"\n  ],\n  \"开放类型\": [\n    \"组合下\"\n  ],\n  \"指标名\": [\n    \"时间差\"\n  ],\n  \"事件1\": [\n    \"募集开始日\"\n  ],\n  \"事件2\": [\n    \"成立日\"\n  ]\n}", "expected_text": "{\n  \"渠道二级分类\": [\n    \"同业机构\"\n  ],\n  \"产品系列\": [\n    \"组合下\"\n  ],\n  \"产品代码\": [\n    \"组合下\"\n  ],\n  \"开放类型\": [\n    \"组合下\"\n  ],\n  \"指标名\": [\n    \"时间差\"\n  ],\n  \"事件1\": [\n    \"募集开始日\"\n  ],\n  \"事件2\": [\n    \"成立日\"\n  ]\n}", "actual_json": {"渠道二级分类": ["同业机构"], "产品系列": ["组合下"], "产品代码": ["组合下"], "开放类型": ["组合下"], "指标名": ["时间差"], "事件1": ["募集开始日"], "事件2": ["成立日"]}, "expected_json": {"渠道二级分类": ["同业机构"], "产品系列": ["组合下"], "产品代码": ["组合下"], "开放类型": ["组合下"], "指标名": ["时间差"], "事件1": ["募集开始日"], "事件2": ["成立日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 248, "actual_text": "{\n  \"产品简称\": [\n    \"至顺5号95期C\"\n  ],\n  \"风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"至顺5号95期C\"\n  ],\n  \"风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ]\n}", "actual_json": {"产品简称": ["至顺5号95期C"], "风险评级": ["R3级 (中等风险)"], "币种": ["人民币"], "指标名": ["业绩基准", "计息基础"]}, "expected_json": {"产品简称": ["至顺5号95期C"], "风险评级": ["R3级 (中等风险)"], "币种": ["人民币"], "指标名": ["业绩基准", "计息基础"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 249, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "产品收益类型": ["货币"], "指标名": ["持仓份额"]}, "expected_json": {"数据日期": ["20250224"], "产品收益类型": ["货币"], "指标名": ["持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 250, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"组合简称\": [\n    \"至顺5号95期\"\n  ],\n  \"产品系列\": [\n    \"至顺\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"成立日\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"组合简称\": [\n    \"至顺5号95期\"\n  ],\n  \"产品系列\": [\n    \"至顺\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"成立日\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "组合简称": ["至顺5号95期"], "产品系列": ["至顺"], "指标名": ["持仓份额", "成立日"], "产品收益类型": ["净值"]}, "expected_json": {"数据日期": ["20250224"], "组合简称": ["至顺5号95期"], "产品系列": ["至顺"], "指标名": ["持仓份额", "成立日"], "产品收益类型": ["净值"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 251, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"成立日\"\n  ],\n  \"产品收益类型\": [\n    \"预期收益\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"成立日\"\n  ],\n  \"产品收益类型\": [\n    \"预期收益\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "指标名": ["持仓份额", "成立日"], "产品收益类型": ["预期收益"], "币种": ["人民币"]}, "expected_json": {"数据日期": ["20250201"], "指标名": ["持仓份额", "成立日"], "产品收益类型": ["预期收益"], "币种": ["人民币"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 252, "actual_text": "{\n  \"组合代码\": [\n    \"启航成长一年定开40号\",\n    \"新启航一年定开24号\",\n    \"天天成长3号1期\"\n  ],\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "expected_text": "{\n  \"组合代码\": [\n    \"启航成长一年定开40号\",\n    \"新启航一年定开24号\",\n    \"天天成长3号1期\"\n  ],\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "actual_json": {"组合代码": ["启航成长一年定开40号", "新启航一年定开24号", "天天成长3号1期"], "数据日期": ["20250224"], "渠道一级分类": ["零售"], "产品收益类型": ["净值"], "指标名": ["募集开始日", "募集结束日"]}, "expected_json": {"组合代码": ["启航成长一年定开40号", "新启航一年定开24号", "天天成长3号1期"], "数据日期": ["20250224"], "渠道一级分类": ["零售"], "产品收益类型": ["净值"], "指标名": ["募集开始日", "募集结束日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 253, "actual_text": "{\n  \"组合名称\": [\n    \"启航成长一年定开40号\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"产品系列\": [\n    \"启航成长\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"上一开放日\"\n  ]\n}", "expected_text": "{\n  \"组合名称\": [\n    \"启航成长一年定开40号\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"产品系列\": [\n    \"启航成长\"\n  ],\n  \"开放类型\": [\n    \"开放式\"\n  ],\n  \"指标名\": [\n    \"上一开放日\"\n  ]\n}", "actual_json": {"组合名称": ["启航成长一年定开40号"], "风险评级": ["R2 级 (中低风险)"], "币种": ["人民币"], "产品系列": ["启航成长"], "开放类型": ["开放式"], "指标名": ["上一开放日"]}, "expected_json": {"组合名称": ["启航成长一年定开40号"], "风险评级": ["R2 级 (中低风险)"], "币种": ["人民币"], "产品系列": ["启航成长"], "开放类型": ["开放式"], "指标名": ["上一开放日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 254, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ],\n  \"组合代码\": [\n    \"母份额产品\"\n  ],\n  \"产品风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"成立日\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ],\n  \"组合代码\": [\n    \"母份额产品\"\n  ],\n  \"产品风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"净值\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"成立日\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "渠道一级分类": ["代销", "零售", "对公"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"], "组合代码": ["母份额产品"], "产品风险评级": ["R2 级 (中低风险)"], "产品收益类型": ["净值"], "运作模式": ["封闭式"], "指标名": ["持仓份额", "成立日"]}, "expected_json": {"数据日期": ["20250224"], "渠道一级分类": ["代销", "零售", "对公"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"], "组合代码": ["母份额产品"], "产品风险评级": ["R2 级 (中低风险)"], "产品收益类型": ["净值"], "运作模式": ["封闭式"], "指标名": ["持仓份额", "成立日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 255, "actual_text": "{\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"产品分类\": [\n    \"人民币货币型\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"产品分类\": [\n    \"人民币货币型\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ]\n}", "actual_json": {"组合简称": ["天天成长B款"], "产品分类": ["人民币货币型"], "指标名": ["业绩基准", "计息基础"]}, "expected_json": {"组合简称": ["天天成长B款"], "产品分类": ["人民币货币型"], "指标名": ["业绩基准", "计息基础"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 256, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"币种\": [\n    \"人民币\",\n    \"美元\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"币种\": [\n    \"人民币\",\n    \"美元\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["零售"], "客户类型": ["个人"], "币种": ["人民币", "美元"], "指标名": ["募集开始日", "募集结束日"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["零售"], "客户类型": ["个人"], "币种": ["人民币", "美元"], "指标名": ["募集开始日", "募集结束日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 257, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"天天成长B款\",\n    \"至顺5号95期C\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\",\n    \"R3级 (中等风险)\"\n  ],\n  \"募集方式\": [\n    \"公募\",\n    \"私募\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓份额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"天天成长B款\",\n    \"至顺5号95期C\"\n  ],\n  \"风险评级\": [\n    \"R1级 (低等风险)\",\n    \"R3级 (中等风险)\"\n  ],\n  \"募集方式\": [\n    \"公募\",\n    \"私募\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓份额\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "产品简称": ["天天成长B款", "至顺5号95期C"], "风险评级": ["R1级 (低等风险)", "R3级 (中等风险)"], "募集方式": ["公募", "私募"], "指标名": ["业绩基准", "持仓份额"]}, "expected_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "产品简称": ["天天成长B款", "至顺5号95期C"], "风险评级": ["R1级 (低等风险)", "R3级 (中等风险)"], "募集方式": ["公募", "私募"], "指标名": ["业绩基准", "持仓份额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 258, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ],\n  \"产品状态\": [\n    \"已到期\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ],\n  \"产品状态\": [\n    \"已到期\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "指标名": ["业绩基准", "计息基础"], "产品状态": ["已到期"]}, "expected_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "指标名": ["业绩基准", "计息基础"], "产品状态": ["已到期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 259, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\",\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\",\n    \"零售\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "组合简称": ["天天成长B款"], "渠道一级分类": ["对公", "零售"], "产品收益类型": ["货币"], "指标名": ["募集开始日", "募集结束日"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "组合简称": ["天天成长B款"], "渠道一级分类": ["对公", "零售"], "产品收益类型": ["货币"], "指标名": ["募集开始日", "募集结束日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 260, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"开放类型含义\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"指标名\": [\n    \"持仓份额\",\n    \"开放类型含义\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "指标名": ["持仓份额", "开放类型含义"]}, "expected_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "指标名": ["持仓份额", "开放类型含义"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 261, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品状态\": [\n    \"已到期\"\n  ],\n  \"产品收益类型\": [\n    \"绝对收益\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ],\n  \"组合代码\": [\n    \"*\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品状态\": [\n    \"已到期\"\n  ],\n  \"产品收益类型\": [\n    \"绝对收益\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"计息基础\"\n  ],\n  \"组合代码\": [\n    \"*\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "产品状态": ["已到期"], "产品收益类型": ["绝对收益"], "指标名": ["业绩基准", "计息基础"], "组合代码": ["*"]}, "expected_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "产品状态": ["已到期"], "产品收益类型": ["绝对收益"], "指标名": ["业绩基准", "计息基础"], "组合代码": ["*"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 262, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"天天成长B\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品简称\": [\n    \"天天成长B\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"募集结束日\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["零售"], "产品简称": ["天天成长B"], "指标名": ["募集开始日", "募集结束日"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "渠道一级分类": ["零售"], "产品简称": ["天天成长B"], "指标名": ["募集开始日", "募集结束日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 263, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"币种\": [\n    \"人民币\",\n    \"美元\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\",\n    \"业绩基准\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R2 级 (中低风险)\"\n  ],\n  \"产品收益类型\": [\n    \"货币\"\n  ],\n  \"币种\": [\n    \"人民币\",\n    \"美元\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"持仓客户数\",\n    \"业绩基准\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "产品收益类型": ["货币"], "币种": ["人民币", "美元"], "指标名": ["持仓规模", "持仓客户数", "业绩基准"]}, "expected_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "风险评级": ["R2 级 (中低风险)"], "产品收益类型": ["货币"], "币种": ["人民币", "美元"], "指标名": ["持仓规模", "持仓客户数", "业绩基准"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 264, "actual_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250224\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "产品分类": ["固定收益类"], "指标名": ["业绩基准", "持仓客户数"]}, "expected_json": {"数据日期": ["20250224"], "渠道一级分类": ["零售"], "产品分类": ["固定收益类"], "指标名": ["业绩基准", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 265, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"成立日\"\n  ],\n  \"产品风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"运作模式\": [\n    \"定开型\"\n  ],\n  \"产品大类明细_企划\": [\n    \"中期\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"客户类型\": [\n    \"个人\",\n    \"机构\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"募集开始日\",\n    \"成立日\"\n  ],\n  \"产品风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"运作模式\": [\n    \"定开型\"\n  ],\n  \"产品大类明细_企划\": [\n    \"中期\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"客户类型\": [\n    \"个人\",\n    \"机构\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "指标名": ["募集开始日", "成立日"], "产品风险评级": ["R3级 (中等风险)"], "运作模式": ["定开型"], "产品大类明细_企划": ["中期"], "币种": ["人民币"], "客户类型": ["个人", "机构"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "指标名": ["募集开始日", "成立日"], "产品风险评级": ["R3级 (中等风险)"], "运作模式": ["定开型"], "产品大类明细_企划": ["中期"], "币种": ["人民币"], "客户类型": ["个人", "机构"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 266, "actual_text": "{\n  \"数据日期\": [\n    \"20250101\",\n    \"20250331\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓客户数\"\n  ],\n  \"产品分类\": [\n    \"市价法养老\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250101\",\n    \"20250331\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓客户数\"\n  ],\n  \"产品分类\": [\n    \"市价法养老\"\n  ]\n}", "actual_json": {"数据日期": ["20250101", "20250331"], "渠道一级分类": ["零售"], "币种": ["人民币"], "指标名": ["业绩基准", "持仓客户数"], "产品分类": ["市价法养老"]}, "expected_json": {"数据日期": ["20250101", "20250331"], "渠道一级分类": ["零售"], "币种": ["人民币"], "指标名": ["业绩基准", "持仓客户数"], "产品分类": ["市价法养老"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 267, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"募集方式\": [\n    \"人民币募集\"\n  ],\n  \"产品特性\": [\n    \"有产品增信标识\",\n    \"外部增级\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"募集方式\": [\n    \"人民币募集\"\n  ],\n  \"产品特性\": [\n    \"有产品增信标识\",\n    \"外部增级\"\n  ],\n  \"指标名\": [\n    \"业绩基准\",\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "募集方式": ["人民币募集"], "产品特性": ["有产品增信标识", "外部增级"], "指标名": ["业绩基准", "持仓客户数"]}, "expected_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售"], "募集方式": ["人民币募集"], "产品特性": ["有产品增信标识", "外部增级"], "指标名": ["业绩基准", "持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 268, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\",\n    \"代销 - 互联网银行\",\n    \"代销 - 国股行\",\n    \"代销 - 同业机构\"\n  ],\n  \"产品简称\": [\n    \"人民币中等风险净值型私募产品\"\n  ],\n  \"产品分类\": [\n    \"权益\",\n    \"混合\",\n    \"固收+\"\n  ],\n  \"产品风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"产品期限\": [\n    \"3M-6M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"下一开放日\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\",\n    \"对公\"\n  ],\n  \"渠道二级分类\": [\n    \"代销 - 中小行\",\n    \"代销 - 互联网银行\",\n    \"代销 - 国股行\",\n    \"代销 - 同业机构\"\n  ],\n  \"产品简称\": [\n    \"人民币中等风险净值型私募产品\"\n  ],\n  \"产品分类\": [\n    \"权益\",\n    \"混合\",\n    \"固收+\"\n  ],\n  \"产品风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"产品期限\": [\n    \"3M-6M\"\n  ],\n  \"指标名\": [\n    \"持仓规模\",\n    \"下一开放日\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售", "对公"], "渠道二级分类": ["代销 - 中小行", "代销 - 互联网银行", "代销 - 国股行", "代销 - 同业机构"], "产品简称": ["人民币中等风险净值型私募产品"], "产品分类": ["权益", "混合", "固收+"], "产品风险评级": ["R3级 (中等风险)"], "产品期限": ["3M-6M"], "指标名": ["持仓规模", "下一开放日"]}, "expected_json": {"数据日期": ["20250201"], "渠道一级分类": ["零售", "对公"], "渠道二级分类": ["代销 - 中小行", "代销 - 互联网银行", "代销 - 国股行", "代销 - 同业机构"], "产品简称": ["人民币中等风险净值型私募产品"], "产品分类": ["权益", "混合", "固收+"], "产品风险评级": ["R3级 (中等风险)"], "产品期限": ["3M-6M"], "指标名": ["持仓规模", "下一开放日"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 269, "actual_text": "提取失败", "expected_text": "提取失败", "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 270, "actual_text": "{\n  \"指标名\": [\n    \"持仓份额占比\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓份额占比\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ]\n}", "actual_json": {"指标名": ["持仓份额占比"], "渠道一级分类": ["零售"]}, "expected_json": {"指标名": ["持仓份额占比"], "渠道一级分类": ["零售"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 271, "actual_text": "{\n  \"数据日期\": [\n    \"20250331\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R4级 (中高风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"开放类型\": [\n    \"开放式\",\n    \"封闭式\"\n  ],\n  \"指标名\": [\n    \"产品期限\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250331\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"风险评级\": [\n    \"R4级 (中高风险)\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"开放类型\": [\n    \"开放式\",\n    \"封闭式\"\n  ],\n  \"指标名\": [\n    \"产品期限\"\n  ]\n}", "actual_json": {"数据日期": ["20250331"], "渠道一级分类": ["零售"], "风险评级": ["R4级 (中高风险)"], "币种": ["人民币"], "开放类型": ["开放式", "封闭式"], "指标名": ["产品期限"]}, "expected_json": {"数据日期": ["20250331"], "渠道一级分类": ["零售"], "风险评级": ["R4级 (中高风险)"], "币种": ["人民币"], "开放类型": ["开放式", "封闭式"], "指标名": ["产品期限"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 272, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模标准差\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模标准差\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售"], "指标名": ["持仓规模标准差"]}, "expected_json": {"渠道一级分类": ["零售"], "指标名": ["持仓规模标准差"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 273, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓份额平均值\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓份额平均值\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "指标名": ["持仓份额平均值"]}, "expected_json": {"数据日期": ["20250201"], "指标名": ["持仓份额平均值"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 274, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓规模中位数\"\n  ],\n  \"维度\": [\n    \"渠道\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓规模中位数\"\n  ],\n  \"维度\": [\n    \"渠道\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "指标名": ["持仓规模中位数"], "维度": ["渠道"]}, "expected_json": {"数据日期": ["20250201"], "指标名": ["持仓规模中位数"], "维度": ["渠道"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 275, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓规模最大\",\n    \"持仓规模最小\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"指标名\": [\n    \"持仓规模最大\",\n    \"持仓规模最小\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "指标名": ["持仓规模最大", "持仓规模最小"]}, "expected_json": {"数据日期": ["20250201"], "指标名": ["持仓规模最大", "持仓规模最小"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 276, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓份额方差\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓份额方差\"\n  ]\n}", "actual_json": {"数据日期": ["20250201"], "产品系列": ["天天成长"], "指标名": ["持仓份额方差"]}, "expected_json": {"数据日期": ["20250201"], "产品系列": ["天天成长"], "指标名": ["持仓份额方差"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 277, "actual_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓份额的几何平均值\"\n  ],\n  \"渠道名称\": [\n    \"不同渠道\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓份额的几何平均值\"\n  ],\n  \"渠道名称\": [\n    \"不同渠道\"\n  ]\n}", "actual_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["持仓份额的几何平均值"], "渠道名称": ["不同渠道"]}, "expected_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["持仓份额的几何平均值"], "渠道名称": ["不同渠道"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 278, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"数据日期\": [\n    \"业务日期\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"数据日期\": [\n    \"业务日期\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "数据日期": ["业务日期"], "渠道一级分类": ["零售"]}, "expected_json": {"指标名": ["持仓规模"], "数据日期": ["业务日期"], "渠道一级分类": ["零售"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 279, "actual_text": "{\n  \"指标名\": [\n    \"持仓份额的加权平均值\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"业务日期\": [\n    \"不同业务日期\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓份额的加权平均值\"\n  ],\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"业务日期\": [\n    \"不同业务日期\"\n  ]\n}", "actual_json": {"指标名": ["持仓份额的加权平均值"], "渠道一级分类": ["零售"], "业务日期": ["不同业务日期"]}, "expected_json": {"指标名": ["持仓份额的加权平均值"], "渠道一级分类": ["零售"], "业务日期": ["不同业务日期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 280, "actual_text": "{\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"维度\": [\n    \"币种\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"维度\": [\n    \"币种\"\n  ]\n}", "actual_json": {"产品简称": ["7天成长4号C"], "指标名": ["持仓份额"], "维度": ["币种"]}, "expected_json": {"产品简称": ["7天成长4号C"], "指标名": ["持仓份额"], "维度": ["币种"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 281, "actual_text": "{\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"指标名\": [\n    \"每份额对应的规模\"\n  ],\n  \"渠道代码\": [\n    \"*\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"指标名\": [\n    \"每份额对应的规模\"\n  ],\n  \"渠道代码\": [\n    \"*\"\n  ]\n}", "actual_json": {"产品简称": ["7天成长4号C"], "指标名": ["每份额对应的规模"], "渠道代码": ["*"]}, "expected_json": {"产品简称": ["7天成长4号C"], "指标名": ["每份额对应的规模"], "渠道代码": ["*"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 282, "actual_text": "{\n  \"组合简称\": [\n    \"天天成长3号1期\"\n  ],\n  \"指标名\": [\n    \"持仓份额占比\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"天天成长3号1期\"\n  ],\n  \"指标名\": [\n    \"持仓份额占比\"\n  ]\n}", "actual_json": {"组合简称": ["天天成长3号1期"], "指标名": ["持仓份额占比"]}, "expected_json": {"组合简称": ["天天成长3号1期"], "指标名": ["持仓份额占比"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 283, "actual_text": "{\n  \"指标名\": [\n    \"持仓份额的调和平均值\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓份额的调和平均值\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ]\n}", "actual_json": {"指标名": ["持仓份额的调和平均值"], "币种": ["人民币"]}, "expected_json": {"指标名": ["持仓份额的调和平均值"], "币种": ["人民币"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 284, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"人民币\"\n  ],\n  \"渠道一级分类\": [\n    \"不同渠道\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"人民币\"\n  ],\n  \"渠道一级分类\": [\n    \"不同渠道\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "产品分类": ["人民币"], "渠道一级分类": ["不同渠道"]}, "expected_json": {"指标名": ["持仓规模"], "产品分类": ["人民币"], "渠道一级分类": ["不同渠道"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 285, "actual_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"统计方法\": [\n    \"众数\"\n  ],\n  \"分组依据\": [\n    \"渠道\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"统计方法\": [\n    \"众数\"\n  ],\n  \"分组依据\": [\n    \"渠道\"\n  ]\n}", "actual_json": {"产品系列": ["天天成长"], "指标名": ["持仓规模"], "统计方法": ["众数"], "分组依据": ["渠道"]}, "expected_json": {"产品系列": ["天天成长"], "指标名": ["持仓规模"], "统计方法": ["众数"], "分组依据": ["渠道"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 286, "actual_text": "{\n  \"组合简称\": [\n    \"7天成长4号\"\n  ],\n  \"指标名\": [\n    \"持仓规模同比增长率\"\n  ],\n  \"数据日期\": [\n    \"业务日期\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"7天成长4号\"\n  ],\n  \"指标名\": [\n    \"持仓规模同比增长率\"\n  ],\n  \"数据日期\": [\n    \"业务日期\"\n  ]\n}", "actual_json": {"组合简称": ["7天成长4号"], "指标名": ["持仓规模同比增长率"], "数据日期": ["业务日期"]}, "expected_json": {"组合简称": ["7天成长4号"], "指标名": ["持仓规模同比增长率"], "数据日期": ["业务日期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 287, "actual_text": "{\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓规模极差\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓规模极差\"\n  ]\n}", "actual_json": {"组合简称": ["天天成长B款"], "指标名": ["持仓规模极差"]}, "expected_json": {"组合简称": ["天天成长B款"], "指标名": ["持仓规模极差"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 288, "actual_text": "{\n  \"组合名称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ]\n}", "expected_text": "{\n  \"组合名称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"产品系列\": [\n    \"天天成长\"\n  ]\n}", "actual_json": {"组合名称": ["天天成长B款"], "指标名": ["持仓份额"], "产品系列": ["天天成长"]}, "expected_json": {"组合名称": ["天天成长B款"], "指标名": ["持仓份额"], "产品系列": ["天天成长"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 289, "actual_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模与持仓份额的乘积总和\"\n  ],\n  \"业务日期\": [\n    \"所有业务日期\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模与持仓份额的乘积总和\"\n  ],\n  \"业务日期\": [\n    \"所有业务日期\"\n  ]\n}", "actual_json": {"产品系列": ["天天成长"], "指标名": ["持仓规模与持仓份额的乘积总和"], "业务日期": ["所有业务日期"]}, "expected_json": {"产品系列": ["天天成长"], "指标名": ["持仓规模与持仓份额的乘积总和"], "业务日期": ["所有业务日期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 290, "actual_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"维度\": [\n    \"币种\"\n  ],\n  \"统计量\": [\n    \"方差\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"维度\": [\n    \"币种\"\n  ],\n  \"统计量\": [\n    \"方差\"\n  ]\n}", "actual_json": {"产品系列": ["天天成长"], "指标名": ["持仓份额"], "维度": ["币种"], "统计量": ["方差"]}, "expected_json": {"产品系列": ["天天成长"], "指标名": ["持仓份额"], "维度": ["币种"], "统计量": ["方差"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 291, "actual_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"运算符\": [\n    \"加权标准差\"\n  ],\n  \"权重\": [\n    \"组合代码下的产品数量\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"天天成长\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"运算符\": [\n    \"加权标准差\"\n  ],\n  \"权重\": [\n    \"组合代码下的产品数量\"\n  ]\n}", "actual_json": {"产品系列": ["天天成长"], "指标名": ["持仓规模"], "运算符": ["加权标准差"], "权重": ["组合代码下的产品数量"]}, "expected_json": {"产品系列": ["天天成长"], "指标名": ["持仓规模"], "运算符": ["加权标准差"], "权重": ["组合代码下的产品数量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 292, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓份额加权总和\"\n  ],\n  \"权重\": [\n    \"市场热度\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓份额加权总和\"\n  ],\n  \"权重\": [\n    \"市场热度\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓份额加权总和"], "权重": ["市场热度"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓份额加权总和"], "权重": ["市场热度"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 293, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"渠道名称\": [\n    \"华夏银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模与持仓份额的相关系数\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"产品简称\": [\n    \"7天成长4号C\"\n  ],\n  \"渠道名称\": [\n    \"华夏银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模与持仓份额的相关系数\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "产品简称": ["7天成长4号C"], "渠道名称": ["华夏银行"], "指标名": ["持仓规模与持仓份额的相关系数"]}, "expected_json": {"数据日期": ["20250515"], "产品简称": ["7天成长4号C"], "渠道名称": ["华夏银行"], "指标名": ["持仓规模与持仓份额的相关系数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 294, "actual_text": "{\n  \"产品简称\": [\n    \"7天成长4号\"\n  ],\n  \"指标名\": [\n    \"持仓份额的平均值\"\n  ],\n  \"渠道名称\": [\n    \"不同渠道\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"7天成长4号\"\n  ],\n  \"指标名\": [\n    \"持仓份额的平均值\"\n  ],\n  \"渠道名称\": [\n    \"不同渠道\"\n  ]\n}", "actual_json": {"产品简称": ["7天成长4号"], "指标名": ["持仓份额的平均值"], "渠道名称": ["不同渠道"]}, "expected_json": {"产品简称": ["7天成长4号"], "指标名": ["持仓份额的平均值"], "渠道名称": ["不同渠道"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 295, "actual_text": "{\n  \"产品简称\": [\n    \"组合7天成长4号C\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓份额总和\"\n  ],\n  \"产品代码\": [\n    \"不同产品代码\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"组合7天成长4号C\"\n  ],\n  \"币种\": [\n    \"人民币\"\n  ],\n  \"指标名\": [\n    \"持仓份额总和\"\n  ],\n  \"产品代码\": [\n    \"不同产品代码\"\n  ]\n}", "actual_json": {"产品简称": ["组合7天成长4号C"], "币种": ["人民币"], "指标名": ["持仓份额总和"], "产品代码": ["不同产品代码"]}, "expected_json": {"产品简称": ["组合7天成长4号C"], "币种": ["人民币"], "指标名": ["持仓份额总和"], "产品代码": ["不同产品代码"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 296, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "运作模式": ["封闭式"]}, "expected_json": {"指标名": ["持仓规模"], "运作模式": ["封闭式"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 297, "actual_text": "{\n  \"渠道二级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前5\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"渠道二级分类\": [\n    \"代销_国股行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前5\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"渠道二级分类": ["代销_国股行"], "指标名": ["持仓规模"], "排序": ["前5"], "字段名": ["产品系列"]}, "expected_json": {"渠道二级分类": ["代销_国股行"], "指标名": ["持仓规模"], "排序": ["前5"], "字段名": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 298, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓规模的平均值\"\n  ],\n  \"维度分组\": [\n    \"渠道\",\n    \"组合\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓规模的平均值\"\n  ],\n  \"维度分组\": [\n    \"渠道\",\n    \"组合\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓规模的平均值"], "维度分组": ["渠道", "组合"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓规模的平均值"], "维度分组": ["渠道", "组合"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 299, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓份额占比\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓份额占比\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓份额占比"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓份额占比"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 300, "actual_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"分析维度\": [\n    \"渠道\",\n    \"产品系列\"\n  ],\n  \"分析方法\": [\n    \"线性回归\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250201\",\n    \"20250225\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"分析维度\": [\n    \"渠道\",\n    \"产品系列\"\n  ],\n  \"分析方法\": [\n    \"线性回归\"\n  ]\n}", "actual_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓规模"], "分析维度": ["渠道", "产品系列"], "分析方法": ["线性回归"]}, "expected_json": {"数据日期": ["20250201", "20250225"], "指标名": ["持仓规模"], "分析维度": ["渠道", "产品系列"], "分析方法": ["线性回归"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 301, "actual_text": "{\n  \"组合简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓份额总和\"\n  ]\n}", "expected_text": "{\n  \"组合简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"渠道一级分类\": [\n    \"对公\"\n  ],\n  \"指标名\": [\n    \"持仓份额总和\"\n  ]\n}", "actual_json": {"组合简称": ["新启航一年定开24号"], "渠道一级分类": ["对公"], "指标名": ["持仓份额总和"]}, "expected_json": {"组合简称": ["新启航一年定开24号"], "渠道一级分类": ["对公"], "指标名": ["持仓份额总和"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 302, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"产品系列\": [\n    \"*\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"产品系列\": [\n    \"*\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "排序": ["前10"], "产品系列": ["*"]}, "expected_json": {"指标名": ["持仓规模"], "排序": ["前10"], "产品系列": ["*"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 303, "actual_text": "{\n  \"产品简称\": [\n    \"启航成长一年定开40号\"\n  ],\n  \"指标名\": [\n    \"持仓份额比例\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"启航成长一年定开40号\"\n  ],\n  \"指标名\": [\n    \"持仓份额比例\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"产品简称": ["启航成长一年定开40号"], "指标名": ["持仓份额比例"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"产品简称": ["启航成长一年定开40号"], "指标名": ["持仓份额比例"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 304, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序条件\": [\n    \"前5\"\n  ],\n  \"筛选条件\": [\n    \"排除现金产品\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序条件\": [\n    \"前5\"\n  ],\n  \"筛选条件\": [\n    \"排除现金产品\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "排序条件": ["前5"], "筛选条件": ["排除现金产品"], "字段名": ["产品系列"]}, "expected_json": {"指标名": ["持仓规模"], "排序条件": ["前5"], "筛选条件": ["排除现金产品"], "字段名": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 305, "actual_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓规模的指数加权移动平均值\"\n  ],\n  \"数据日期\": [\n    \"各业务日期\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"指标名\": [\n    \"持仓规模的指数加权移动平均值\"\n  ],\n  \"数据日期\": [\n    \"各业务日期\"\n  ]\n}", "actual_json": {"产品简称": ["天天成长B款"], "指标名": ["持仓规模的指数加权移动平均值"], "数据日期": ["各业务日期"]}, "expected_json": {"产品简称": ["天天成长B款"], "指标名": ["持仓规模的指数加权移动平均值"], "数据日期": ["各业务日期"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 306, "actual_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"数据日期\": [\n    \"业务日期\"\n  ],\n  \"指标名\": [\n    \"持仓规模的变化率\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"天天成长B款\"\n  ],\n  \"数据日期\": [\n    \"业务日期\"\n  ],\n  \"指标名\": [\n    \"持仓规模的变化率\"\n  ]\n}", "actual_json": {"产品简称": ["天天成长B款"], "数据日期": ["业务日期"], "指标名": ["持仓规模的变化率"]}, "expected_json": {"产品简称": ["天天成长B款"], "数据日期": ["业务日期"], "指标名": ["持仓规模的变化率"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 307, "actual_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"分位数分析\": [\n    \"\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"分位数分析\": [\n    \"\"\n  ]\n}", "actual_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["持仓规模"], "分位数分析": [""]}, "expected_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["持仓规模"], "分位数分析": [""]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 308, "actual_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"产品系列\": [\n    \"新启航\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"新启航一年定开24号\"\n  ],\n  \"指标名\": [\n    \"持仓份额\"\n  ],\n  \"产品系列\": [\n    \"新启航\"\n  ]\n}", "actual_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["持仓份额"], "产品系列": ["新启航"]}, "expected_json": {"产品简称": ["新启航一年定开24号"], "指标名": ["持仓份额"], "产品系列": ["新启航"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 309, "actual_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序条件\": [\n    \"前五\"\n  ],\n  \"字段名\": [\n    \"企划费率期限\"\n  ]\n}", "expected_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序条件\": [\n    \"前五\"\n  ],\n  \"字段名\": [\n    \"企划费率期限\"\n  ]\n}", "actual_json": {"渠道名称": ["招商银行"], "指标名": ["持仓规模"], "排序条件": ["前五"], "字段名": ["企划费率期限"]}, "expected_json": {"渠道名称": ["招商银行"], "指标名": ["持仓规模"], "排序条件": ["前五"], "字段名": ["企划费率期限"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 310, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ],\n  \"企划费率期限\": [\n    \"不同封闭期限\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"运作模式\": [\n    \"封闭式\"\n  ],\n  \"企划费率期限\": [\n    \"不同封闭期限\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "运作模式": ["封闭式"], "企划费率期限": ["不同封闭期限"]}, "expected_json": {"指标名": ["持仓规模"], "运作模式": ["封闭式"], "企划费率期限": ["不同封闭期限"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 311, "actual_text": "{\n  \"产品期限\": [\n    \"3Y 以上\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ]\n}", "expected_text": "{\n  \"产品期限\": [\n    \"3Y 以上\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ]\n}", "actual_json": {"产品期限": ["3Y 以上"], "指标名": ["持仓规模"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"]}, "expected_json": {"产品期限": ["3Y 以上"], "指标名": ["持仓规模"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 312, "actual_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"企划费率期限\": [\n    \"不同的产品期限\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"企划费率期限\": [\n    \"不同的产品期限\"\n  ]\n}", "actual_json": {"产品系列": ["新启航"], "指标名": ["持仓规模"], "企划费率期限": ["不同的产品期限"]}, "expected_json": {"产品系列": ["新启航"], "指标名": ["持仓规模"], "企划费率期限": ["不同的产品期限"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 313, "actual_text": "{\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"募集方式\": [\n    \"私募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"募集方式\": [\n    \"私募\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"客户类型": ["机构"], "募集方式": ["私募"], "指标名": ["持仓规模"], "维度": ["产品系列"]}, "expected_json": {"客户类型": ["机构"], "募集方式": ["私募"], "指标名": ["持仓规模"], "维度": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 314, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"产品系列\": [\n    \"启航增强\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"产品系列\": [\n    \"启航增强\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "排序": ["前10"], "产品系列": ["启航增强"]}, "expected_json": {"指标名": ["持仓规模"], "排序": ["前10"], "产品系列": ["启航增强"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 315, "actual_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前5\"\n  ],\n  \"产品系列\": [\n    \"*\"\n  ]\n}", "expected_text": "{\n  \"渠道一级分类\": [\n    \"零售\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前5\"\n  ],\n  \"产品系列\": [\n    \"*\"\n  ]\n}", "actual_json": {"渠道一级分类": ["零售"], "指标名": ["持仓规模"], "排序": ["前5"], "产品系列": ["*"]}, "expected_json": {"渠道一级分类": ["零售"], "指标名": ["持仓规模"], "排序": ["前5"], "产品系列": ["*"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 316, "actual_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"产品系列": ["新启航"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"产品系列": ["新启航"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 317, "actual_text": "{\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"产品分类": ["固收+"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"产品分类": ["固收+"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 318, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "指标名": ["持仓规模"], "维度": ["产品系列"]}, "expected_json": {"数据日期": ["20250515"], "指标名": ["持仓规模"], "维度": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 319, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"风险评级\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"维度\": [\n    \"风险评级\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "维度": ["风险评级"]}, "expected_json": {"指标名": ["持仓规模"], "维度": ["风险评级"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 320, "actual_text": "{\n  \"客户类型\": [\n    \"机构\",\n    \"个人\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"字段名3\": [\n    \"产品名称\"\n  ]\n}", "expected_text": "{\n  \"客户类型\": [\n    \"机构\",\n    \"个人\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"字段名3\": [\n    \"产品名称\"\n  ]\n}", "actual_json": {"客户类型": ["机构", "个人"], "指标名": ["持仓规模"], "排序": ["前10"], "字段名3": ["产品名称"]}, "expected_json": {"客户类型": ["机构", "个人"], "指标名": ["持仓规模"], "排序": ["前10"], "字段名3": ["产品名称"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 321, "actual_text": "{\n  \"数据日期\": [\n    \"20241231\"\n  ],\n  \"指标名\": [\n    \"持仓规模增长量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20241231\"\n  ],\n  \"指标名\": [\n    \"持仓规模增长量\"\n  ]\n}", "actual_json": {"数据日期": ["20241231"], "指标名": ["持仓规模增长量"]}, "expected_json": {"数据日期": ["20241231"], "指标名": ["持仓规模增长量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 322, "actual_text": "{\n  \"数据日期\": [\n    \"20241231\"\n  ],\n  \"指标名\": [\n    \"增长量\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20241231\"\n  ],\n  \"指标名\": [\n    \"增长量\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ]\n}", "actual_json": {"数据日期": ["20241231"], "指标名": ["增长量"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"]}, "expected_json": {"数据日期": ["20241231"], "指标名": ["增长量"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 323, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"排除条件\": [\n    \"总行\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"排除条件\": [\n    \"总行\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "渠道一级分类": ["代销"], "排除条件": ["总行"]}, "expected_json": {"指标名": ["持仓规模"], "渠道一级分类": ["代销"], "排除条件": ["总行"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 324, "actual_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"附加条件\": [\n    \"不同开发频率\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"附加条件\": [\n    \"不同开发频率\"\n  ]\n}", "actual_json": {"产品系列": ["新启航"], "指标名": ["持仓规模"], "附加条件": ["不同开发频率"]}, "expected_json": {"产品系列": ["新启航"], "指标名": ["持仓规模"], "附加条件": ["不同开发频率"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 325, "actual_text": "{\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"客户类型": ["个人"], "指标名": ["持仓规模"]}, "expected_json": {"客户类型": ["个人"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 326, "actual_text": "{\n  \"风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"风险评级\": [\n    \"R3级 (中等风险)\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"风险评级": ["R3级 (中等风险)"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"风险评级": ["R3级 (中等风险)"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 327, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"企划费率期限\": [\n    \"不同的产品期限\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"企划费率期限\": [\n    \"不同的产品期限\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "企划费率期限": ["不同的产品期限"]}, "expected_json": {"指标名": ["持仓规模"], "企划费率期限": ["不同的产品期限"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 328, "actual_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前五\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前五\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"渠道名称": ["招商银行"], "指标名": ["持仓规模"], "排序": ["前五"], "字段名": ["产品系列"]}, "expected_json": {"渠道名称": ["招商银行"], "指标名": ["持仓规模"], "排序": ["前五"], "字段名": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 329, "actual_text": "{\n  \"渠道名称\": [\n    \"直销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ]\n}", "expected_text": "{\n  \"渠道名称\": [\n    \"直销\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品系列\": [\n    \"智享\",\n    \"启元添利\",\n    \"悦享\",\n    \"优享增强\",\n    \"新 N 天\",\n    \"私享\",\n    \"启航创利稳进\",\n    \"稳健成长\",\n    \"灵活策略\",\n    \"安鑫\",\n    \"新启航\",\n    \"新安鑫\",\n    \"新卓越\",\n    \"智联\",\n    \"灵活宝\",\n    \"启航创利\",\n    \"安顺\",\n    \"日添利尊享\",\n    \"新稳健\",\n    \"启元策略\",\n    \"优享\",\n    \"优选安享\",\n    \"QDII\",\n    \"天天成长\",\n    \"启元稳利\",\n    \"安臻\",\n    \"启航增强\",\n    \"启明\",\n    \"鑫享\",\n    \"安心合盈\",\n    \"日添利\",\n    \"卓越\",\n    \"坤润\",\n    \"百川\",\n    \"安心稳健\",\n    \"启航成长\",\n    \"安盈成长\",\n    \"至顺\",\n    \"启元四季\",\n    \"老 N 天\",\n    \"至顺睿驰\",\n    \"被动指数\",\n    \"尊享多策略\",\n    \"启元增强\",\n    \"星辰\",\n    \"灵活成长\",\n    \"平安优选\"\n  ]\n}", "actual_json": {"渠道名称": ["直销"], "指标名": ["持仓规模"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"]}, "expected_json": {"渠道名称": ["直销"], "指标名": ["持仓规模"], "产品系列": ["智享", "启元添利", "悦享", "优享增强", "新 N 天", "私享", "启航创利稳进", "稳健成长", "灵活策略", "安鑫", "新启航", "新安鑫", "新卓越", "智联", "灵活宝", "启航创利", "安顺", "日添利尊享", "新稳健", "启元策略", "优享", "优选安享", "QDII", "天天成长", "启元稳利", "安臻", "启航增强", "启明", "鑫享", "安心合盈", "日添利", "卓越", "坤润", "百川", "安心稳健", "启航成长", "安盈成长", "至顺", "启元四季", "老 N 天", "至顺睿驰", "被动指数", "尊享多策略", "启元增强", "星辰", "灵活成长", "平安优选"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 330, "actual_text": "{\n  \"渠道名称\": [\n    \"招商银行\",\n    \"总行渠道\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"渠道名称\": [\n    \"招商银行\",\n    \"总行渠道\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"渠道名称": ["招商银行", "总行渠道"], "指标名": ["持仓规模"]}, "expected_json": {"渠道名称": ["招商银行", "总行渠道"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 331, "actual_text": "{\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"产品分类": ["固收+"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"产品分类": ["固收+"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 332, "actual_text": "{\n  \"产品简称\": [\n    \"新启航一年定开\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"产品简称\": [\n    \"新启航一年定开\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"产品简称": ["新启航一年定开"], "指标名": ["持仓规模"]}, "expected_json": {"产品简称": ["新启航一年定开"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 333, "actual_text": "{\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"年初\",\n    \"现在\"\n  ]\n}", "expected_text": "{\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"时间范围\": [\n    \"年初\",\n    \"现在\"\n  ]\n}", "actual_json": {"产品分类": ["固收+"], "指标名": ["持仓规模"], "时间范围": ["年初", "现在"]}, "expected_json": {"产品分类": ["固收+"], "指标名": ["持仓规模"], "时间范围": ["年初", "现在"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 334, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"对比时间\": [\n    \"上月同期\"\n  ],\n  \"增长量\": [\n    \"增长了多少\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"固收+\"\n  ],\n  \"对比时间\": [\n    \"上月同期\"\n  ],\n  \"增长量\": [\n    \"增长了多少\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "产品分类": ["固收+"], "对比时间": ["上月同期"], "增长量": ["增长了多少"]}, "expected_json": {"指标名": ["持仓规模"], "产品分类": ["固收+"], "对比时间": ["上月同期"], "增长量": ["增长了多少"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 335, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"现金类\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"现金类\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "产品分类": ["现金类"]}, "expected_json": {"指标名": ["持仓规模"], "产品分类": ["现金类"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 336, "actual_text": "{\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比时间1\": [\n    \"上日\"\n  ],\n  \"对比时间2\": [\n    \"上月\"\n  ],\n  \"对比时间3\": [\n    \"去年\"\n  ]\n}", "expected_text": "{\n  \"客户类型\": [\n    \"机构\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"对比时间1\": [\n    \"上日\"\n  ],\n  \"对比时间2\": [\n    \"上月\"\n  ],\n  \"对比时间3\": [\n    \"去年\"\n  ]\n}", "actual_json": {"客户类型": ["机构"], "指标名": ["持仓规模"], "对比时间1": ["上日"], "对比时间2": ["上月"], "对比时间3": ["去年"]}, "expected_json": {"客户类型": ["机构"], "指标名": ["持仓规模"], "对比时间1": ["上日"], "对比时间2": ["上月"], "对比时间3": ["去年"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 337, "actual_text": "{\n  \"产品分类\": [\n    \"含权\"\n  ],\n  \"指标名\": [\n    \"持仓规模排名\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"产品分类\": [\n    \"含权\"\n  ],\n  \"指标名\": [\n    \"持仓规模排名\"\n  ],\n  \"排序\": [\n    \"前10\"\n  ],\n  \"字段名\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"产品分类": ["含权"], "指标名": ["持仓规模排名"], "排序": ["前10"], "字段名": ["产品系列"]}, "expected_json": {"产品分类": ["含权"], "指标名": ["持仓规模排名"], "排序": ["前10"], "字段名": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 338, "actual_text": "{\n  \"产品期限\": [\n    \"7天\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"现金类\"\n  ]\n}", "expected_text": "{\n  \"产品期限\": [\n    \"7天\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"现金类\"\n  ]\n}", "actual_json": {"产品期限": ["7天"], "指标名": ["持仓规模"], "产品分类": ["现金类"]}, "expected_json": {"产品期限": ["7天"], "指标名": ["持仓规模"], "产品分类": ["现金类"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 339, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前5\"\n  ],\n  \"客户类型\": [\n    \"产品\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序\": [\n    \"前5\"\n  ],\n  \"客户类型\": [\n    \"产品\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "排序": ["前5"], "客户类型": ["产品"]}, "expected_json": {"指标名": ["持仓规模"], "排序": ["前5"], "客户类型": ["产品"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 340, "actual_text": "{\n  \"产品开放频率\": [\n    \"按季度开放\",\n    \"按月开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"产品开放频率\": [\n    \"按季度开放\",\n    \"按月开放\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"产品开放频率": ["按季度开放", "按月开放"], "指标名": ["持仓规模"]}, "expected_json": {"产品开放频率": ["按季度开放", "按月开放"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 341, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模占比\"\n  ],\n  \"产品分类\": [\n    \"不同分类\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模占比\"\n  ],\n  \"产品分类\": [\n    \"不同分类\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模占比"], "产品分类": ["不同分类"]}, "expected_json": {"指标名": ["持仓规模占比"], "产品分类": ["不同分类"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 342, "actual_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"排序条件\": [\n    \"前5\"\n  ],\n  \"字段名3\": [\n    \"产品系列\"\n  ]\n}", "expected_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"排序条件\": [\n    \"前5\"\n  ],\n  \"字段名3\": [\n    \"产品系列\"\n  ]\n}", "actual_json": {"渠道名称": ["招商银行"], "指标名": ["持仓客户数"], "排序条件": ["前5"], "字段名3": ["产品系列"]}, "expected_json": {"渠道名称": ["招商银行"], "指标名": ["持仓客户数"], "排序条件": ["前5"], "字段名3": ["产品系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 343, "actual_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"排序条件\": [\n    \"前5\"\n  ],\n  \"字段名3\": [\n    \"产品期限\"\n  ]\n}", "expected_text": "{\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓客户数\"\n  ],\n  \"排序条件\": [\n    \"前5\"\n  ],\n  \"字段名3\": [\n    \"产品期限\"\n  ]\n}", "actual_json": {"渠道名称": ["招商银行"], "指标名": ["持仓客户数"], "排序条件": ["前5"], "字段名3": ["产品期限"]}, "expected_json": {"渠道名称": ["招商银行"], "指标名": ["持仓客户数"], "排序条件": ["前5"], "字段名3": ["产品期限"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 344, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序指标\": [\n    \"持仓规模\"\n  ],\n  \"排序方式\": [\n    \"降序\"\n  ],\n  \"筛选条件\": [\n    \"运作模式=封闭式\",\n    \"运作模式=日开型\"\n  ],\n  \"返回字段\": [\n    \"产品简称\"\n  ],\n  \"数量\": [\n    \"5\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"排序指标\": [\n    \"持仓规模\"\n  ],\n  \"排序方式\": [\n    \"降序\"\n  ],\n  \"筛选条件\": [\n    \"运作模式=封闭式\",\n    \"运作模式=日开型\"\n  ],\n  \"返回字段\": [\n    \"产品简称\"\n  ],\n  \"数量\": [\n    \"5\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "排序指标": ["持仓规模"], "排序方式": ["降序"], "筛选条件": ["运作模式=封闭式", "运作模式=日开型"], "返回字段": ["产品简称"], "数量": ["5"]}, "expected_json": {"指标名": ["持仓规模"], "排序指标": ["持仓规模"], "排序方式": ["降序"], "筛选条件": ["运作模式=封闭式", "运作模式=日开型"], "返回字段": ["产品简称"], "数量": ["5"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 345, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模波动率\"\n  ],\n  \"时间范围\": [\n    \"近半年\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模波动率\"\n  ],\n  \"时间范围\": [\n    \"近半年\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["持仓规模波动率"], "时间范围": ["近半年"]}, "expected_json": {"数据日期": ["20250515"], "渠道名称": ["招商银行"], "指标名": ["持仓规模波动率"], "时间范围": ["近半年"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 346, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 347, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 348, "actual_text": "{\n  \"数据日期\": [\n    \"20241231\"\n  ],\n  \"指标名\": [\n    \"持仓规模增长量\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20241231\"\n  ],\n  \"指标名\": [\n    \"持仓规模增长量\"\n  ]\n}", "actual_json": {"数据日期": ["20241231"], "指标名": ["持仓规模增长量"]}, "expected_json": {"数据日期": ["20241231"], "指标名": ["持仓规模增长量"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 349, "actual_text": "{\n  \"估值时效\": [\n    \"T0\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"估值时效\": [\n    \"T0\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"估值时效": ["T0"], "指标名": ["持仓规模"]}, "expected_json": {"估值时效": ["T0"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 350, "actual_text": "{\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"统计方式\": [\n    \"平均值\"\n  ],\n  \"排序方式\": [\n    \"降序\"\n  ],\n  \"排序字段\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"客户类型\": [\n    \"个人\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"统计方式\": [\n    \"平均值\"\n  ],\n  \"排序方式\": [\n    \"降序\"\n  ],\n  \"排序字段\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"客户类型": ["个人"], "指标名": ["持仓规模"], "统计方式": ["平均值"], "排序方式": ["降序"], "排序字段": ["持仓规模"]}, "expected_json": {"客户类型": ["个人"], "指标名": ["持仓规模"], "统计方式": ["平均值"], "排序方式": ["降序"], "排序字段": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 351, "actual_text": "{\n  \"估值时效\": [\n    \"T2\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"估值时效\": [\n    \"T2\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"估值时效": ["T2"], "指标名": ["持仓规模"]}, "expected_json": {"估值时效": ["T2"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 352, "actual_text": "{\n  \"估值时效\": [\n    \"T0\"\n  ],\n  \"指标名\": [\n    \"持仓规模同比增长\"\n  ]\n}", "expected_text": "{\n  \"估值时效\": [\n    \"T0\"\n  ],\n  \"指标名\": [\n    \"持仓规模同比增长\"\n  ]\n}", "actual_json": {"估值时效": ["T0"], "指标名": ["持仓规模同比增长"]}, "expected_json": {"估值时效": ["T0"], "指标名": ["持仓规模同比增长"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 353, "actual_text": "{\n  \"估值时效\": [\n    \"T0\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"估值时效\": [\n    \"T0\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"估值时效": ["T0"], "指标名": ["持仓规模"]}, "expected_json": {"估值时效": ["T0"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 354, "actual_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"渠道名称\": [\n    \"中国银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"产品系列\": [\n    \"新启航\"\n  ],\n  \"渠道名称\": [\n    \"中国银行\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"产品系列": ["新启航"], "渠道名称": ["中国银行"], "指标名": ["持仓规模"]}, "expected_json": {"产品系列": ["新启航"], "渠道名称": ["中国银行"], "指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 355, "actual_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"比较基准\": [\n    \"前一天\"\n  ],\n  \"排序指标\": [\n    \"跌幅\"\n  ],\n  \"排序数量\": [\n    \"5\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"比较基准\": [\n    \"前一天\"\n  ],\n  \"排序指标\": [\n    \"跌幅\"\n  ],\n  \"排序数量\": [\n    \"5\"\n  ]\n}", "actual_json": {"数据日期": ["20250514"], "指标名": ["持仓规模"], "比较基准": ["前一天"], "排序指标": ["跌幅"], "排序数量": ["5"]}, "expected_json": {"数据日期": ["20250514"], "指标名": ["持仓规模"], "比较基准": ["前一天"], "排序指标": ["跌幅"], "排序数量": ["5"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 356, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"持仓规模占总规模比例\"\n  ],\n  \"排序\": [\n    \"最大\"\n  ],\n  \"数量\": [\n    \"5\"\n  ],\n  \"维度\": [\n    \"渠道名称\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"指标名\": [\n    \"持仓规模占总规模比例\"\n  ],\n  \"排序\": [\n    \"最大\"\n  ],\n  \"数量\": [\n    \"5\"\n  ],\n  \"维度\": [\n    \"渠道名称\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "指标名": ["持仓规模占总规模比例"], "排序": ["最大"], "数量": ["5"], "维度": ["渠道名称"]}, "expected_json": {"数据日期": ["20250515"], "指标名": ["持仓规模占总规模比例"], "排序": ["最大"], "数量": ["5"], "维度": ["渠道名称"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 357, "actual_text": "{\n  \"指标名\": [\n    \"风险评级\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"风险评级\"\n  ],\n  \"客户类型\": [\n    \"机构\"\n  ]\n}", "actual_json": {"指标名": ["风险评级"], "客户类型": ["机构"]}, "expected_json": {"指标名": ["风险评级"], "客户类型": ["机构"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 358, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"]}, "expected_json": {"指标名": ["持仓规模"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 359, "actual_text": "{\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓客户数\"\n  ]\n}", "actual_json": {"指标名": ["持仓客户数"]}, "expected_json": {"指标名": ["持仓客户数"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 360, "actual_text": "{\n  \"产品分类\": [\n    \"封闭日开非现金\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "expected_text": "{\n  \"产品分类\": [\n    \"封闭日开非现金\"\n  ],\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\",\n    \"零售\",\n    \"对公\"\n  ]\n}", "actual_json": {"产品分类": ["封闭日开非现金"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "expected_json": {"产品分类": ["封闭日开非现金"], "指标名": ["持仓规模"], "渠道一级分类": ["代销", "零售", "对公"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 361, "actual_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"日开\",\n    \"非现金类\"\n  ],\n  \"维度\": [\n    \"系列\"\n  ]\n}", "expected_text": "{\n  \"指标名\": [\n    \"持仓规模\"\n  ],\n  \"产品分类\": [\n    \"日开\",\n    \"非现金类\"\n  ],\n  \"维度\": [\n    \"系列\"\n  ]\n}", "actual_json": {"指标名": ["持仓规模"], "产品分类": ["日开", "非现金类"], "维度": ["系列"]}, "expected_json": {"指标名": ["持仓规模"], "产品分类": ["日开", "非现金类"], "维度": ["系列"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 362, "actual_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品简称\": [\n    \"灵活成长\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250514\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品简称\": [\n    \"灵活成长\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250514"], "产品分类": ["固定收益类"], "产品简称": ["灵活成长"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250514"], "产品分类": ["固定收益类"], "产品简称": ["灵活成长"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 363, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品简称\": [\n    \"灵活成长\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"产品简称\": [\n    \"灵活成长\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品简称": ["灵活成长"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "产品简称": ["灵活成长"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 364, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"私享\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"私享\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品系列": ["私享"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品系列": ["私享"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 365, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"产品分类\": [\n    \"混合\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ],\n  \"产品分类\": [\n    \"混合\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["净申赎金额"], "产品分类": ["混合"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["净申赎金额"], "产品分类": ["混合"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 366, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 367, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 368, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 369, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 370, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 371, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 372, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 373, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 374, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 375, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 376, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 377, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 378, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开1号\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开1号"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 379, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 380, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 381, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 382, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 383, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 384, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 385, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 386, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 387, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 388, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 389, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 390, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 391, "actual_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"直销\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250515\"\n  ],\n  \"渠道名称\": [\n    \"直销\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"指标名\": [\n    \"净申赎金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250515"], "渠道名称": ["直销"], "产品分类": ["固定收益类"], "指标名": ["净申赎金额"]}, "expected_json": {"数据日期": ["20250515"], "渠道名称": ["直销"], "产品分类": ["固定收益类"], "指标名": ["净申赎金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 392, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长添利日开30天持有3号\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长添利日开30天持有3号"], "产品分类": ["固定收益类"], "渠道名称": ["国有股份制银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 393, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长汇稳28天持有\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长汇稳28天持有\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长汇稳28天持有"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长汇稳28天持有"], "产品分类": ["固定收益类"], "渠道名称": ["招商银行"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 394, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长汇稳28天持有\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"平安理财灵活成长汇稳28天持有\"\n  ],\n  \"产品分类\": [\n    \"固定收益类\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长汇稳28天持有"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["平安理财灵活成长汇稳28天持有"], "产品分类": ["固定收益类"], "渠道一级分类": ["代销"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 395, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"指标名\": [\n    \"申购金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "指标名": ["申购金额"]}, "expected_json": {"数据日期": ["20250320"], "指标名": ["申购金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 396, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 397, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"新启航三个月定开4号A\"\n  ],\n  \"渠道一级分类\": [\n    \"代销\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["新启航三个月定开4号A"], "渠道一级分类": ["代销"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 398, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"启航创利\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品系列\": [\n    \"启航创利\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品系列": ["启航创利"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品系列": ["启航创利"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 399, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"国有股份制银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["国有股份制银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 400, "actual_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "expected_text": "{\n  \"数据日期\": [\n    \"20250320\"\n  ],\n  \"产品简称\": [\n    \"启航创利\"\n  ],\n  \"渠道名称\": [\n    \"招商银行\"\n  ],\n  \"指标名\": [\n    \"赎回金额\"\n  ]\n}", "actual_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "expected_json": {"数据日期": ["20250320"], "产品简称": ["启航创利"], "渠道名称": ["招商银行"], "指标名": ["赎回金额"]}, "is_consistent": true, "details": "一致", "comparison_result": "一致"}, {"row_index": 401, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 402, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 403, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 404, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 405, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 406, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 407, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 408, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 409, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 410, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 411, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 412, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 413, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 414, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 415, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 416, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 417, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 418, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 419, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 420, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 421, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 422, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 423, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 424, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 425, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 426, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 427, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 428, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 429, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 430, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 431, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 432, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 433, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 434, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 435, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 436, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 437, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 438, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 439, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 440, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 441, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 442, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 443, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 444, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 445, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 446, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 447, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 448, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 449, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 450, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 451, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 452, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 453, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 454, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 455, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 456, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 457, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 458, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 459, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 460, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 461, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 462, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 463, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 464, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 465, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 466, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 467, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 468, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 469, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 470, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 471, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 472, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 473, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 474, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 475, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 476, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 477, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 478, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 479, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 480, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 481, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 482, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 483, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 484, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 485, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 486, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 487, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 488, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 489, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 490, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 491, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 492, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 493, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 494, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 495, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 496, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 497, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 498, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 499, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 500, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 501, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 502, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 503, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 504, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 505, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 506, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 507, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 508, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 509, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 510, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 511, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 512, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 513, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 514, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 515, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 516, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 517, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 518, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 519, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 520, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 521, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 522, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 523, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 524, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 525, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 526, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 527, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 528, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 529, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 530, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 531, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 532, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 533, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 534, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 535, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 536, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 537, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 538, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 539, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 540, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 541, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 542, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 543, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 544, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}, {"row_index": 545, "actual_text": NaN, "expected_text": NaN, "actual_json": null, "expected_json": null, "is_consistent": true, "details": "两者都为空", "comparison_result": "一致"}]