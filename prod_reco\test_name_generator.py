"""
测试产品名称生成器
"""

from generate_random_names import ProductNameGenerator
import pandas as pd

def test_name_extraction():
    """测试名称提取功能"""
    print("🧪 测试名称提取功能")
    print("=" * 40)
    
    # 创建生成器实例
    generator = ProductNameGenerator()
    
    # 测试用例
    test_cases = [
        "福建上杭农村商业银行",
        "中国工商银行股份有限公司",
        "招商银行股份有限公司",
        "平安理财有限责任公司",
        "天弘基金管理有限公司",
        "华夏银行股份有限公司",
        "建设银行理财子公司",
        "上海浦东发展银行",
        "兴业银行股份有限公司",
        "中信银行股份有限公司",
        "光大银行股份有限公司",
        "民生银行股份有限公司",
        "北京银行股份有限公司",
        "江苏银行股份有限公司",
        "宁波银行股份有限公司",
        "南京银行股份有限公司",
        "杭州银行股份有限公司",
        "成都银行股份有限公司",
        "长沙银行股份有限公司",
        "青岛银行股份有限公司"
    ]
    
    print("测试结果:")
    for i, test_name in enumerate(test_cases, 1):
        random_name = generator.extract_key_chars(test_name)
        print(f"  {i:2d}. '{test_name}' -> '{random_name}'")
    
    print(f"\n生成的随机名称数量: {len(generator.generated_names)}")
    print(f"随机名称列表: {list(generator.generated_names)}")

def test_duplicate_handling():
    """测试重复处理"""
    print("\n🧪 测试重复处理")
    print("=" * 40)
    
    generator = ProductNameGenerator()
    
    # 测试相同名称多次生成
    test_name = "中国工商银行股份有限公司"
    
    print(f"多次生成 '{test_name}' 的随机名称:")
    for i in range(5):
        random_name = generator.extract_key_chars(test_name)
        print(f"  第 {i+1} 次: '{random_name}'")

def create_test_excel():
    """创建测试用的Excel文件"""
    print("\n🧪 创建测试Excel文件")
    print("=" * 40)
    
    # 测试数据
    test_data = {
        '序号': list(range(1, 21)),
        '真实值': [
            "福建上杭农村商业银行",
            "中国工商银行股份有限公司", 
            "招商银行股份有限公司",
            "平安理财有限责任公司",
            "天弘基金管理有限公司",
            "华夏银行股份有限公司",
            "建设银行理财子公司",
            "上海浦东发展银行",
            "兴业银行股份有限公司",
            "中信银行股份有限公司",
            "光大银行股份有限公司",
            "民生银行股份有限公司",
            "北京银行股份有限公司",
            "江苏银行股份有限公司",
            "宁波银行股份有限公司",
            "南京银行股份有限公司",
            "杭州银行股份有限公司",
            "成都银行股份有限公司",
            "长沙银行股份有限公司",
            "青岛银行股份有限公司"
        ]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(test_data)
    
    # 保存为Excel文件
    test_file = "产品库_测试.xlsx"
    df.to_excel(test_file, index=False)
    print(f"测试Excel文件已创建: {test_file}")
    
    return test_file

def test_full_process():
    """测试完整流程"""
    print("\n🧪 测试完整流程")
    print("=" * 40)
    
    # 创建测试Excel文件
    test_file = create_test_excel()
    
    # 创建生成器
    generator = ProductNameGenerator(test_file)
    
    # 加载Excel文件
    if not generator.load_excel():
        print("❌ 加载测试文件失败")
        return
    
    print("✅ 测试文件加载成功")
    
    # 生成随机名称
    if not generator.generate_all_random_names():
        print("❌ 生成随机名称失败")
        return
    
    print("✅ 随机名称生成成功")
    
    # 预览结果
    print("\n📋 生成结果:")
    generator.preview_results(20)
    
    # 保存文件
    if generator.save_excel():
        print("✅ 文件保存成功")
    else:
        print("❌ 文件保存失败")

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况")
    print("=" * 40)
    
    generator = ProductNameGenerator()
    
    edge_cases = [
        "",  # 空字符串
        None,  # None值
        "A",  # 单个字符
        "AB",  # 两个字符
        "123",  # 纯数字
        "ABC123",  # 字母数字混合
        "中",  # 单个中文字符
        "中国",  # 两个中文字符
        "!@#$%",  # 特殊字符
        "中国工商银行股份有限公司北京分行营业部",  # 超长名称
    ]
    
    print("边界情况测试结果:")
    for i, test_case in enumerate(edge_cases, 1):
        try:
            random_name = generator.extract_key_chars(test_case)
            print(f"  {i:2d}. '{test_case}' -> '{random_name}'")
        except Exception as e:
            print(f"  {i:2d}. '{test_case}' -> 错误: {str(e)}")

def test_pattern_matching():
    """测试模式匹配"""
    print("\n🧪 测试模式匹配")
    print("=" * 40)
    
    generator = ProductNameGenerator()
    
    # 按类型分组测试
    test_groups = {
        "银行类": [
            "中国工商银行股份有限公司",
            "招商银行股份有限公司",
            "福建上杭农村商业银行",
            "北京农商银行",
            "上海农村信用社"
        ],
        "理财类": [
            "平安理财有限责任公司",
            "建信理财有限责任公司",
            "工银理财有限责任公司"
        ],
        "基金类": [
            "天弘基金管理有限公司",
            "华夏基金管理有限公司",
            "易方达基金管理有限公司"
        ],
        "保险类": [
            "中国平安保险股份有限公司",
            "中国人寿保险股份有限公司",
            "太平洋保险股份有限公司"
        ]
    }
    
    for group_name, test_cases in test_groups.items():
        print(f"\n{group_name}:")
        for test_case in test_cases:
            random_name = generator.extract_key_chars(test_case)
            print(f"  '{test_case}' -> '{random_name}'")

def main():
    """主测试函数"""
    print("🚀 产品名称生成器测试套件")
    print("=" * 60)
    
    # 运行所有测试
    test_name_extraction()
    test_duplicate_handling()
    test_edge_cases()
    test_pattern_matching()
    test_full_process()
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")
    
    print("\n💡 使用说明:")
    print("1. 将产品库.xlsx文件放在prod_reco目录中")
    print("2. 运行 python generate_random_names.py")
    print("3. 程序会自动为第二列的真实值生成随机名称并保存到第三列")
    print("4. 原文件会被更新，同时会创建带时间戳的备份文件")

if __name__ == "__main__":
    main()
