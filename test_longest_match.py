"""
测试最长匹配功能
对比修改前后的匹配结果
"""

from question_rule_matcher import QuestionRuleMatcher

def test_longest_match():
    """测试最长匹配功能"""
    print("🧪 测试最长匹配功能")
    print("=" * 60)
    
    # 创建匹配器
    matcher = QuestionRuleMatcher()
    
    # 测试问题
    test_questions = [
        "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？",
        "昨天天天成长3号在小招的赎回金额？",
        "新启航三个月定开1号产品净申赎金额是多少？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 测试问题 {i}: {question}")
        
        # 使用新的最长匹配方法
        longest_result = matcher.match_question(question)
        print(f"最长匹配结果: {longest_result}")
        
        # 使用保留的所有匹配方法
        all_results = matcher.match_question_all(question)
        print(f"所有匹配结果: {all_results}")
        
        # 对比分析
        print(f"匹配项数量对比: 最长匹配={len(longest_result)}, 所有匹配={len(all_results)}")
        
        if longest_result and all_results:
            longest_key = list(longest_result.keys())[0]
            longest_value = longest_result[longest_key]
            longest_length = len(longest_value.replace(" ", ""))
            
            print(f"最长匹配详情: {longest_key} = {longest_value} (长度: {longest_length})")
            
            print("所有匹配详情:")
            for key, value in all_results.items():
                length = len(value.replace(" ", ""))
                is_longest = "← 最长" if key == longest_key else ""
                print(f"  {key} = {value} (长度: {length}) {is_longest}")

def test_edge_cases():
    """测试边界情况"""
    print("\n🧪 测试边界情况")
    print("=" * 60)
    
    matcher = QuestionRuleMatcher()
    
    edge_cases = [
        ("空问题", ""),
        ("无匹配问题", "这是一个完全不相关的问题"),
        ("单字符问题", "A"),
        ("只有标点符号", "？！。，")
    ]
    
    for case_name, question in edge_cases:
        print(f"\n测试 {case_name}: '{question}'")
        result = matcher.match_question(question)
        print(f"结果: {result}")

def demonstrate_usage():
    """演示使用方法"""
    print("\n🚀 使用方法演示")
    print("=" * 60)
    
    from question_rule_matcher import match_question_simple
    
    # 示例问题
    question = "平安理财启元策略日开270天持有4号固收类理财产品B的规模是多少？"
    
    print(f"问题: {question}")
    
    # 方法1：使用简化函数（现在返回最长匹配）
    result = match_question_simple(question)
    print(f"简化函数结果: {result}")
    
    # 方法2：使用类方法
    matcher = QuestionRuleMatcher()
    
    # 最长匹配
    longest_match = matcher.match_question(question)
    print(f"最长匹配: {longest_match}")
    
    # 所有匹配（如果需要的话）
    all_matches = matcher.match_question_all(question)
    print(f"所有匹配: {all_matches}")

def main():
    """主函数"""
    print("🎯 最长匹配功能测试")
    print("=" * 80)
    
    try:
        # 测试最长匹配
        test_longest_match()
        
        # 测试边界情况
        test_edge_cases()
        
        # 演示使用方法
        demonstrate_usage()
        
        print("\n" + "=" * 80)
        print("🎉 测试完成！")
        
        print("\n📝 总结:")
        print("✅ match_question() 方法现在只返回最长匹配")
        print("✅ match_question_all() 方法保留了返回所有匹配的功能")
        print("✅ match_question_simple() 函数也使用最长匹配逻辑")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
