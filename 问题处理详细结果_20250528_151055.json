{"processing_info": {"total_questions": 10, "successful": 10, "failed": 0, "processing_time": "2025-05-28T15:10:55.479273"}, "results": [{"index": 0, "question": "3. 提取本月第一天权益类产品的预约申购金额和预约赎回金额？", "extracted_keywords": {"数据日期": ["20250501-20250531"], "产品分类": ["权益"], "指标名": ["预约申购金额", "预约赎回金额"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250501-20250531\"],\n  \"产品分类\": [\"权益\"],\n  \"指标名\": [\"预约申购金额\", \"预约赎回金额\"]\n}\n```", "processing_time": 2.0180625915527344, "success": true, "error": null}, {"index": 1, "question": "9. 提取启航系列的产品，在近三天内各渠道的持仓客户数分布。", "extracted_keywords": {"数据日期": ["20250513-20250515"], "产品系列": ["启航"], "指标名": ["持仓客户数"], "渠道一级分类": ["代销", "零售", "对公"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250513-20250515\"],\n  \"产品系列\": [\"启航\"],\n  \"指标名\": [\"持仓客户数\"],\n  \"渠道一级分类\": [\"代销\", \"零售\", \"对公\"]\n}\n```", "processing_time": 2.1780076026916504, "success": true, "error": null}, {"index": 2, "question": "5. 获取过去 20 天中， 中小行渠道且产品大类为 ' 固收 + ' 的产品的预约赎回金额汇总", "extracted_keywords": {"数据日期": ["20250425-20250515"], "渠道二级分类": ["代销 - 中小行"], "产品分类": ["固收+"], "指标名": ["预约赎回金额"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250425-20250515\"],\n  \"渠道二级分类\": [\"代销 - 中小行\"],\n  \"产品分类\": [\"固收+\"],\n  \"指标名\": [\"预约赎回金额\"]\n}\n```", "processing_time": 2.673861026763916, "success": true, "error": null}, {"index": 3, "question": "4. 哪种渠道一级分类的持仓规模最大，是否随时间变化而改变？", "extracted_keywords": {"渠道一级分类": ["代销", "零售", "对公"], "指标名": ["持仓规模"]}, "model_output": "```json\n{\n  \"渠道一级分类\": [\"代销\", \"零售\", \"对公\"],\n  \"指标名\": [\"持仓规模\"]\n}\n```", "processing_time": 2.0908069610595703, "success": true, "error": null}, {"index": 4, "question": "10. 持仓份额和持仓规模之间的比例关系在不同产品大类中是否一致？", "extracted_keywords": {"指标名": ["持仓份额", "持仓规模"], "产品分类": ["固定收益类", "固收 +", "混合", "权益", "现金类", "老理财"]}, "model_output": "```json\n{\n  \"指标名\": [\"持仓份额\", \"持仓规模\"],\n  \"产品分类\": [\"固定收益类\", \"固收 +\", \"混合\", \"权益\", \"现金类\", \"老理财\"]\n}\n```", "processing_time": 1.8276910781860352, "success": true, "error": null}, {"index": 5, "question": "19. 综合考虑渠道、产品大类和业务日期，哪些因素对净申赎金额（N_NET_SG_AMT）的影响最为显著？", "extracted_keywords": {"字段名1": ["渠道名称", "产品分类", "数据日期"], "字段名2": ["净申赎金额"]}, "model_output": "```json\n{\n  \"字段名1\": [\"渠道名称\", \"产品分类\", \"数据日期\"],\n  \"字段名2\": [\"净申赎金额\"]\n}\n```", "processing_time": 1.3084843158721924, "success": true, "error": null}, {"index": 6, "question": "对于人民币理财产品，不同产品期限含义的产品占比分别是多少？", "extracted_keywords": {"产品期限": ["人民币理财产品"], "指标名": ["产品期限占比"]}, "model_output": "```json\n{\n  \"产品期限\": [\"人民币理财产品\"],\n  \"指标名\": [\"产品期限占比\"]\n}\n```", "processing_time": 1.4861834049224854, "success": true, "error": null}, {"index": 7, "question": "20240224，对公和零售渠道下，产品至顺1号69期和新启航一年定开24号的持仓份额和持仓规模分别是多少？", "extracted_keywords": {"数据日期": ["20240224-20240224"], "渠道一级分类": ["对公", "零售"], "产品简称": ["至顺1号69期", "新启航一年定开24号"], "指标名": ["持仓份额", "持仓规模"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20240224-20240224\"],\n  \"渠道一级分类\": [\"对公\", \"零售\"],\n  \"产品简称\": [\"至顺1号69期\", \"新启航一年定开24号\"],\n  \"指标名\": [\"持仓份额\", \"持仓规模\"]\n}\n```", "processing_time": 3.040146827697754, "success": true, "error": null}, {"index": 8, "question": "20240224，零售渠道的产品，按照产品和币种维度下的持仓规模是多少？？", "extracted_keywords": {"数据日期": ["20240224-20240224"], "渠道一级分类": ["零售"], "指标名": ["持仓规模"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20240224-20240224\"],\n  \"渠道一级分类\": [\"零售\"],\n  \"指标名\": [\"持仓规模\"]\n}\n```", "processing_time": 5.2475666999816895, "success": true, "error": null}, {"index": 9, "question": "20240201，零售渠道下，人民币中高风险净值型产品，业绩基准和持仓规模是多少？", "extracted_keywords": {"数据日期": ["20240201-20240201"], "渠道一级分类": ["零售"], "风险评级": ["R3级 (中等风险)"], "产品收益类型": ["净值"], "指标名": ["业绩基准", "持仓规模"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20240201-20240201\"],\n  \"渠道一级分类\": [\"零售\"],\n  \"风险评级\": [\"R3级 (中等风险)\"],\n  \"产品收益类型\": [\"净值\"],\n  \"指标名\": [\"业绩基准\", \"持仓规模\"]\n}\n```", "processing_time": 2.0014185905456543, "success": true, "error": null}]}