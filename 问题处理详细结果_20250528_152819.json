{"processing_info": {"total_questions": 5, "successful": 5, "failed": 0, "processing_time": "2025-05-28T15:28:19.266794"}, "results": [{"index": 0, "question": "3. 提取本月第一天权益类产品的预约申购金额和预约赎回金额？", "extracted_keywords": {"数据日期": ["20250501-20250531"], "产品分类": ["权益"], "指标名": ["申购金额", "赎回金额"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250501-20250531\"],\n  \"产品分类\": [\"权益\"],\n  \"指标名\": [\"申购金额\", \"赎回金额\"]\n}\n```", "processing_time": 2.5346689224243164, "success": true, "error": null}, {"index": 1, "question": "9. 提取启航系列的产品，在近三天内各渠道的持仓客户数分布。", "extracted_keywords": {"产品系列": ["启航"], "数据日期": ["20250526-20250528"], "指标名": ["持仓客户数"]}, "model_output": "```json\n{\n  \"产品系列\": [\"启航\"],\n  \"数据日期\": [\"20250526-20250528\"],\n  \"指标名\": [\"持仓客户数\"]\n}\n```", "processing_time": 1.6627349853515625, "success": true, "error": null}, {"index": 2, "question": "5. 获取过去 20 天中， 中小行渠道且产品大类为 ' 固收 + ' 的产品的预约赎回金额汇总", "extracted_keywords": {"数据日期": ["20250428-20250517"], "渠道一级分类": ["代销"], "渠道二级分类": ["代销 - 中小行"], "产品分类": ["固收 +"], "指标名": ["赎回金额"]}, "model_output": "```json\n{\n  \"数据日期\": [\"20250428-20250517\"],\n  \"渠道一级分类\": [\"代销\"],\n  \"渠道二级分类\": [\"代销 - 中小行\"],\n  \"产品分类\": [\"固收 +\"],\n  \"指标名\": [\"赎回金额\"]\n}\n```", "processing_time": 2.7275993824005127, "success": true, "error": null}, {"index": 3, "question": "4. 哪种渠道一级分类的持仓规模最大，是否随时间变化而改变？", "extracted_keywords": {"渠道一级分类": ["代销", "零售", "对公"], "指标名": ["持仓规模"]}, "model_output": "```json\n{\n  \"渠道一级分类\": [\"代销\", \"零售\", \"对公\"],\n  \"指标名\": [\"持仓规模\"]\n}\n```", "processing_time": 1.682753324508667, "success": true, "error": null}, {"index": 4, "question": "10. 持仓份额和持仓规模之间的比例关系在不同产品大类中是否一致？", "extracted_keywords": {"产品大类明细_企划": ["固定收益类", "固收 +", "混合", "权益", "现金类", "老理财"], "指标名": ["持仓份额", "持仓规模"]}, "model_output": "```json\n{\n  \"产品大类明细_企划\": [\"固定收益类\", \"固收 +\", \"混合\", \"权益\", \"现金类\", \"老理财\"],\n  \"指标名\": [\"持仓份额\", \"持仓规模\"]\n}\n```", "processing_time": 2.635378360748291, "success": true, "error": null}]}